@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom placeholder styles */
.placeholder-sm::placeholder {
  font-size: 0.875rem; /* 14px */
}

.placeholder-xs::placeholder {
  font-size: 0.8125rem; /* 13px - slightly larger than before */
}

/* Date input specific styles */
input[type="date"].placeholder-xs::-webkit-datetime-edit-text,
input[type="date"].placeholder-xs::-webkit-datetime-edit-month-field,
input[type="date"].placeholder-xs::-webkit-datetime-edit-day-field,
input[type="date"].placeholder-xs::-webkit-datetime-edit-year-field {
  font-size: 0.8125rem !important; /* 13px */
}

input[type="date"].placeholder-xs::-webkit-datetime-edit-fields-wrapper {
  font-size: 0.8125rem !important; /* 13px */
}

input[type="date"].placeholder-xs::-webkit-input-placeholder {
  font-size: 0.8125rem !important; /* 13px */
}

/* Select dropdown styles */
.select-xs {
  font-size: 0.875rem; /* 14px for main text */
}

.select-xs option {
  font-size: 0.8125rem !important; /* 13px for options */
}

/* Specific styling for the default option */
.select-xs option[value=""] {
  font-size: 0.8125rem !important; /* 13px */
  color: #6b7280; /* gray-500 */
}

/* Force smaller text in select when no option is selected */
.select-xs:invalid {
  font-size: 0.8125rem !important; /* 13px */
  color: #6b7280; /* gray-500 */
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Custom scrollbar for TOC */
.toc-container::-webkit-scrollbar {
  width: 4px;
}

.toc-container::-webkit-scrollbar-track {
  background: #F0F2F2;
}

.toc-container::-webkit-scrollbar-thumb {
  background: #C1D4D9;
  border-radius: 2px;
}

.toc-container::-webkit-scrollbar-thumb:hover {
  background: #9BB9BF;
}

/* Blog content table styling */
.blog-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
  font-size: 0.875rem;
  line-height: 1.5;
  @apply bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-sm;
}

.blog-content table th,
.blog-content table td {
  padding: 0.75rem 1rem;
  text-align: left;
  @apply border-b border-gray-200 dark:border-gray-700;
  vertical-align: top;
}

.blog-content table th {
  @apply bg-gray-50 dark:bg-gray-700 font-semibold text-gray-900 dark:text-gray-100 border-b-2 border-gray-300 dark:border-gray-600;
}

.blog-content table td {
  @apply text-gray-800 dark:text-gray-200;
}

.blog-content table tr:hover {
  @apply bg-gray-50 dark:bg-gray-700/50;
}

.blog-content table tr:hover td {
  @apply text-gray-900 dark:text-gray-100;
}

.blog-content table tr:last-child td {
  border-bottom: none;
}

/* Responsive table */
@media (max-width: 768px) {
  .blog-content table,
  .project-content table {
    font-size: 0.8rem;
  }

  .blog-content table th,
  .blog-content table td,
  .project-content table th,
  .project-content table td {
    padding: 0.5rem 0.75rem;
  }
}

/* Smooth scroll behavior - only on desktop to avoid mobile conflicts */
@media (min-width: 768px) {
  html {
    scroll-behavior: smooth;
  }
}

/* Mobile-optimized scrolling */
@media (max-width: 767px) {
  html {
    scroll-behavior: auto !important;
    -webkit-overflow-scrolling: touch;
  }

  body {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-y: contain;
    touch-action: pan-y;
  }

  /* Disable smooth scrolling on all interactive elements */
  a, button {
    scroll-behavior: auto !important;
  }
}

html {
  background: white; /* Default background */
}

html.dark {
  background: #111827; /* Dark background */
}

/* Enhanced Blog content styling */
.blog-content h1,
.blog-content h2,
.blog-content h3,
.blog-content h4 {
  scroll-margin-top: 100px;
  font-family: inherit;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.025em;
}

.blog-content h2 {
  @apply text-3xl font-bold mt-12 mb-6 text-gray-900 dark:text-gray-100;
  position: relative;
  padding-bottom: 0.5rem;
}

.blog-content h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 3rem;
  height: 3px;
  background: linear-gradient(90deg, #f97316, #ea580c);
  border-radius: 2px;
}

.blog-content h3 {
  @apply text-2xl font-semibold mt-10 mb-4 text-gray-800 dark:text-gray-200;
  position: relative;
}

.blog-content h3::before {
  content: '';
  position: absolute;
  left: -1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 1.5rem;
  background: linear-gradient(180deg, #3b82f6, #1d4ed8);
  border-radius: 2px;
}

.blog-content h4 {
  @apply text-xl font-medium mt-8 mb-3 text-gray-700 dark:text-gray-300;
}

.blog-content p {
  @apply mb-6 leading-relaxed text-gray-800 dark:text-gray-200;
  font-size: 1.125rem;
  line-height: 1.75;
}

.blog-content ul,
.blog-content ol {
  @apply mb-6 pl-0 text-gray-800 dark:text-gray-200;
  font-size: 1.125rem;
  line-height: 1.75;
}

.blog-content ul {
  list-style: none;
  margin-left: 1.5rem;
}

.blog-content ul li {
  @apply mb-3 text-gray-800 dark:text-gray-200 relative pl-8;
  position: relative;
}

.blog-content ul li::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.75rem;
  width: 6px;
  height: 6px;
  background: linear-gradient(135deg, #f97316, #ea580c);
  border-radius: 50%;
  transform: translateY(-50%);
}

.blog-content ol {
  counter-reset: list-counter;
  margin-left: 1.5rem;
}

.blog-content ol li {
  @apply mb-3 text-gray-800 dark:text-gray-200 relative pl-8;
  counter-increment: list-counter;
}

.blog-content ol li::before {
  content: counter(list-counter);
  position: absolute;
  left: 0;
  top: 0;
  width: 1.5rem;
  height: 1.5rem;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
}

.blog-content blockquote {
  @apply border-l-4 border-orange-400 pl-8 pr-6 py-6 my-8 italic text-gray-700 dark:text-gray-300 rounded-r-lg;
  background: linear-gradient(135deg, #fff7ed, #fed7aa);
  position: relative;
  font-size: 1.25rem;
  line-height: 1.6;
}

.dark .blog-content blockquote {
  background: linear-gradient(135deg, #431407, #7c2d12);
}

.blog-content blockquote::before {
  content: '"';
  position: absolute;
  left: 1rem;
  top: 0.5rem;
  font-size: 3rem;
  color: #f97316;
  opacity: 0.3;
  font-family: Georgia, serif;
  line-height: 1;
}

.blog-content blockquote p {
  @apply mb-0 relative z-10;
  margin-left: 1rem;
}

/* Inline code styling */
.blog-content code {
  @apply px-2 py-1 rounded text-sm font-mono;
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  color: #1e293b;
  border: 1px solid #cbd5e1;
  font-size: 0.875rem;
}

.dark .blog-content code {
  background: linear-gradient(135deg, #1e293b, #334155);
  color: #e2e8f0;
  border: 1px solid #475569;
}

/* Code block styling */
.blog-content pre {
  @apply rounded-xl overflow-hidden my-8 shadow-lg;
  background: linear-gradient(135deg, #0f172a, #1e293b);
  border: 1px solid #334155;
  position: relative;
}

.blog-content pre::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2.5rem;
  background: linear-gradient(135deg, #1e293b, #334155);
  border-bottom: 1px solid #475569;
}

.blog-content pre::after {
  content: '⚫ ⚫ ⚫';
  position: absolute;
  top: 0.75rem;
  left: 1rem;
  color: #64748b;
  font-size: 0.75rem;
  z-index: 10;
}

.blog-content pre code {
  @apply bg-transparent p-0 block;
  color: #e2e8f0;
  padding: 3rem 1.5rem 1.5rem;
  font-size: 0.875rem;
  line-height: 1.6;
  overflow-x: auto;
}

.blog-content strong {
  @apply font-bold text-gray-900 dark:text-gray-100;
}

.blog-content em {
  @apply italic text-gray-800 dark:text-gray-200;
}

.blog-content a {
  @apply text-orange-500 hover:text-red-600 no-underline transition-all duration-200 font-medium;
  position: relative;
  background: linear-gradient(to right, #f97316, #ea580c);
  background-size: 0% 2px;
  background-position: 0 100%;
  background-repeat: no-repeat;
  transition: background-size 0.3s ease, color 0.2s ease;
}

.blog-content a:hover {
  background-size: 100% 2px;
}

/* Enhanced image styling */
.blog-content img {
  @apply rounded-xl shadow-lg my-8 w-full h-auto;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #e2e8f0;
}

.dark .blog-content img {
  border: 1px solid #374151;
}

.blog-content img:hover {
  transform: scale(1.02);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.dark .blog-content img:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

/* Image with caption styling */
.blog-content figure {
  @apply my-8;
}

.blog-content figure img {
  @apply mb-3;
}

.blog-content figcaption {
  @apply text-sm text-gray-600 dark:text-gray-400 text-center italic;
  font-size: 0.875rem;
  line-height: 1.4;
}

/* Project content styling */
.project-content h1,
.project-content h2,
.project-content h3,
.project-content h4 {
  scroll-margin-top: 100px;
}

.project-content h2 {
  @apply text-2xl font-bold mt-8 mb-4 text-gray-900 dark:text-gray-100;
}

.project-content h3 {
  @apply text-xl font-semibold mt-6 mb-3 text-gray-900 dark:text-gray-200;
}

.project-content h4 {
  @apply text-lg font-medium mt-4 mb-2 text-gray-900 dark:text-gray-300;
}

.project-content p {
  @apply mb-4 leading-relaxed text-gray-900 dark:text-gray-200;
}

.project-content ul,
.project-content ol {
  @apply mb-4 pl-6 text-gray-900 dark:text-gray-200;
}

.project-content ul {
  list-style: none;
}

.project-content ul li {
  @apply mb-2 text-gray-900 dark:text-gray-200 relative pl-6;
}

.project-content ul li::before {
  content: "•";
  color: #FB923C; /* orange-400 */
  font-weight: bold;
  position: absolute;
  left: 0;
  font-size: 1.2em;
  line-height: 1.2;
}

.project-content ol li {
  @apply mb-2 text-gray-900 dark:text-gray-200;
}

.project-content blockquote {
  @apply border-l-4 border-neutral-300 dark:border-neutral-600 pl-6 pr-6 py-6 italic my-8 text-gray-700 dark:text-gray-400 bg-neutral-50 dark:bg-neutral-800 rounded-lg shadow-sm;
}

.project-content code {
  @apply bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm font-mono text-gray-900 dark:text-gray-200;
}

.project-content pre {
  @apply bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto my-4;
}

.project-content img {
  @apply my-6 w-full h-auto;
}

.project-content a {
  @apply text-orange-500 hover:text-red-600 no-underline transition-colors duration-200 font-medium;
}

/* Project content table styling */
.project-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
  font-size: 0.875rem;
  line-height: 1.5;
  @apply bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-sm;
}

.project-content table th,
.project-content table td {
  padding: 0.75rem 1rem;
  text-align: left;
  @apply border-b border-gray-200 dark:border-gray-700;
  vertical-align: top;
}

.project-content table th {
  @apply bg-gray-50 dark:bg-gray-700 font-semibold text-gray-900 dark:text-gray-100 border-b-2 border-gray-300 dark:border-gray-600;
}

.project-content table td {
  @apply text-gray-800 dark:text-gray-200;
}

.project-content table tr:hover {
  @apply bg-gray-50 dark:bg-gray-700/50;
}

.project-content table tr:hover td {
  @apply text-gray-900 dark:text-gray-100;
}

.project-content table tr:last-child td {
  border-bottom: none;
}

/* Grid patterns for light and dark modes */
.bg-grid-pattern-light {
  background-image: radial-gradient(circle, #e5e7eb 1px, transparent 1px);
  background-size: 20px 20px;
}

.bg-grid-pattern-dark {
  background-image: radial-gradient(circle, #374151 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Marquee animation for northeast direction */
@keyframes marquee-ne {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(4px, -4px);
  }
  50% {
    transform: translate(8px, -8px);
  }
  75% {
    transform: translate(4px, -4px);
  }
  100% {
    transform: translate(0, 0);
  }
}

.animate-marquee-ne {
  animation: marquee-ne 1s ease-in-out infinite;
}

/* Mobile scroll optimizations */
@media (max-width: 767px) {
  /* Mobile scrolling optimizations */
  html, body {
    scroll-behavior: auto !important;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-y: contain;
    touch-action: pan-y;
  }

  /* Ensure content is visible on mobile */
  main {
    position: relative;
    z-index: 10;
    min-height: calc(100vh - 80px);
  }

  /* Ensure hero section is visible */
  .text-center {
    position: relative;
    z-index: 20;
  }





  /* Optimize tap highlights and scrolling */
  * {
    /* Disable smooth scrolling completely */
    scroll-behavior: auto !important;
    /* Remove tap highlights and callouts */
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
  }

  /* Enable text selection for content areas */
  .blog-content *,
  .project-content *,
  p, h1, h2, h3, h4, h5, h6, span, div {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }

  /* Basic mobile container optimizations */
  .max-w-7xl,
  .max-w-6xl {
    overscroll-behavior: contain;
  }

  /* Optimize animations for mobile performance */
  .animate-fade-in,
  .animate-slide-up {
    animation-duration: 0.3s !important;
    animation-timing-function: ease-out !important;
  }



  /* Ensure modal is properly centered on mobile */
  .fixed.inset-0 {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 9999 !important;
  }

  /* Mobile modal specific fixes */
  @media (max-width: 640px) {
    .fixed.inset-0 {
      padding: 1rem !important;
      min-height: 100vh !important;
      min-height: 100dvh !important; /* Dynamic viewport height for mobile */
    }

    /* Ensure modal content is properly sized on mobile */
    .fixed.inset-0 > div {
      width: 100% !important;
      max-width: calc(100vw - 2rem) !important;
      max-height: calc(100vh - 2rem) !important;
      max-height: calc(100dvh - 2rem) !important;
      margin: auto !important;
    }
  }



  /* Optimize CSS transitions for mobile */
  .transition-all,
  .transition-colors,
  .transition-opacity {
    transition-duration: 0.15s !important;
  }
}



@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --white: #ffffff;
    --black: #000000;
    --transparent: transparent;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}



@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Global link styling */
  a:not(.no-link-style) {
    @apply text-orange-500 hover:text-red-600 transition-colors duration-200;
  }
}
