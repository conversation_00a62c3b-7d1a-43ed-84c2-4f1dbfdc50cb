rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Media files - users can only access their own files
    match /media/{userId}/{allPaths=**} {
      allow read: if true; // Anyone can read (for public blog images)
      allow write, delete: if request.auth != null && request.auth.uid == userId;
    }
    
    // Blog images - users can only manage their own images
    match /blog-images/{userId}/{allPaths=**} {
      allow read: if true; // Anyone can read (for public blog images)
      allow write, delete: if request.auth != null && request.auth.uid == userId;
    }
    
    // Project images - users can only manage their own images
    match /project-images/{userId}/{allPaths=**} {
      allow read: if true; // Anyone can read (for public project images)
      allow write, delete: if request.auth != null && request.auth.uid == userId;
    }
  }
}
