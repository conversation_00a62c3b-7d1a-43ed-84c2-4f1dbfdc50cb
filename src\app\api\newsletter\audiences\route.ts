import { NextResponse } from 'next/server';
import mailchimp from '@mailchimp/mailchimp_marketing';

// Initialize Mailchimp
mailchimp.setConfig({
  apiKey: process.env.MAILCHIMP_API_KEY,
  server: process.env.MAILCHIMP_API_KEY?.split('-')[1] || 'us12',
});

export async function GET() {
  try {
    if (!process.env.MAILCHIMP_API_KEY) {
      return NextResponse.json(
        { error: 'Mailchimp API key not configured' },
        { status: 500 }
      );
    }

    // Get all audiences/lists
    const response = await mailchimp.lists.getAllLists();
    
    const audiences = response.lists.map((list: any) => ({
      id: list.id,
      name: list.name,
      memberCount: list.stats.member_count,
      subscribeUrlShort: list.subscribe_url_short,
      dateCreated: list.date_created,
    }));

    return NextResponse.json({
      success: true,
      audiences,
      message: `Found ${audiences.length} audience(s)`,
    });
  } catch (error: any) {
    console.error('Error fetching Mailchimp audiences:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch audiences',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
