'use client'

import { useEffect, useState } from 'react'
import { getBlogPosts } from '@/lib/firebase-operations'
import { processMarkdownContent } from '@/lib/markdown-client'
import BlogGridWithFilter from '@/components/BlogGridWithFilter'
import SubtleG<PERSON>ientBackground from '@/components/SubtleGradientBackground'
import { BlogPost, DatabaseBlogPost } from '@/types'

// Note: Metadata moved to layout.tsx since this is now a client component

// Convert Firebase posts to BlogPost format
async function convertFirebasePostToBlogPost(firebasePost: DatabaseBlogPost): Promise<BlogPost> {
  // Process markdown content
  const processedContent = await processMarkdownContent(firebasePost.content)

  return {
    slug: firebasePost.slug,
    title: firebasePost.title,
    excerpt: firebasePost.excerpt,
    date: firebasePost.scheduled_for || firebasePost.created_at,
    featuredImage: firebasePost.featured_image || '/images/blog/default.png',
    content: processedContent,
    readTime: firebasePost.reading_time || 5,
    tags: firebasePost.tags || [],
    author: '<PERSON>',
    categories: firebasePost.categories || [],
  }
}

export default function BlogPage() {
  const [posts, setPosts] = useState<BlogPost[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function loadPosts() {
      try {
        // Get posts from Firebase only
        const firebasePosts = await getBlogPosts()

        // Convert Firebase posts to BlogPost format and filter published posts
        const publishedPosts = firebasePosts.filter(post => post.published)
        const convertedPosts = await Promise.all(
          publishedPosts.map(post => convertFirebasePostToBlogPost(post))
        )
        const sortedPosts = convertedPosts.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())

        setPosts(sortedPosts)
      } catch (error) {
        console.error('Error loading posts:', error)
        setPosts([])
      } finally {
        setLoading(false)
      }
    }

    loadPosts()
  }, [])

  // Structured data for SEO
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Blog",
    "name": "Ernst Romelo Blog",
    "description": "Insights on web development, AI automation, and technology",
    "url": "https://ernestomelo.com/blog",
    "author": {
      "@type": "Person",
      "name": "Ernst Romelo",
      "url": "https://ernestomelo.com"
    },
    "blogPost": posts.map(post => ({
      "@type": "BlogPosting",
      "headline": post.title,
      "description": post.excerpt,
      "url": `https://ernestomelo.com/blog/${post.slug}`,
      "datePublished": post.date,
      "author": {
        "@type": "Person",
        "name": post.author || "Ernst Romelo"
      },
      "image": post.featuredImage
    }))
  }

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <SubtleGradientBackground />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-gray-100 mb-6 animate-fade-in">
            Blog
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto animate-slide-up">
            Explore my thoughts on web development, AI automation, and technology.
          </p>
        </div>

        {/* Blog Grid with Filter */}
        <div className="mb-20">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <BlogGridWithFilter posts={posts} />
          )}
        </div>
      </div>
    </>
  )
}
