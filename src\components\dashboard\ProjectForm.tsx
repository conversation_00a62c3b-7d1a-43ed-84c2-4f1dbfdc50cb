'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/providers/AuthProvider'
import { createProject, updateProject, getProject } from '@/lib/firebase-operations'
import { DatabaseProject } from '@/types'

import FeaturedImageSelector from './FeaturedImageSelector'

interface ProjectFormProps {
  projectId?: string
  mode: 'create' | 'edit'
}

export default function ProjectForm({ projectId, mode }: ProjectFormProps) {
  const { user } = useAuth()
  const router = useRouter()
  
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    content: '',
    featured_image: '',
    published: false,
    scheduled_for: '',
    tags: [] as string[],
    project_url: '',
    github_url: '',
    tech_stack: [] as string[],
    // Project metadata
    client: '',
    industry: '',
    project_date: '',
    technology_used: [] as string[],
    challenge: '',
    solution: '',
  })
  
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    if (mode === 'edit' && projectId) {
      loadProject()
    }
  }, [mode, projectId])

  const loadProject = async () => {
    if (!projectId) return
    
    setLoading(true)
    try {
      const project = await getProject(projectId)
      if (project) {
        setFormData({
          title: project.title,
          description: project.description || '',
          content: project.content,
          featured_image: project.featured_image || '',
          published: project.published,
          scheduled_for: project.scheduled_for || '',
          tags: project.tags || [],
          project_url: project.project_url || '',
          github_url: project.github_url || '',
          tech_stack: project.tech_stack || [],
          client: project.client || '',
          industry: project.industry || '',
          project_date: project.project_date || '',
          technology_used: project.technology_used || [],
          challenge: project.challenge || '',
          solution: project.solution || '',
        })
      }
    } catch (error) {
      console.error('Error loading project:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }))
  }



  const handleSubmit = async (e: React.FormEvent, isDraft = false) => {
    e.preventDefault()
    if (!user) return

    setSaving(true)
    try {
      const projectData = {
        ...formData,
        published: isDraft ? false : !!formData.project_date, // Base publishing on project_date
        scheduled_for: formData.project_date, // Use project_date as scheduled_for
        technology_used: formData.tech_stack, // Map tech_stack to technology_used for consistency
      }

      if (mode === 'create') {
        const newProjectId = await createProject(projectData, user.uid)
        router.push(`/dashboard/projects/${newProjectId}/edit`)
      } else if (projectId) {
        await updateProject(projectId, projectData)
        router.push('/dashboard/projects')
      }
    } catch (error) {
      console.error('Error saving project:', error)
      alert('Failed to save project')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto">
      <form onSubmit={(e) => handleSubmit(e)} className="space-y-8">
        {/* Basic Information */}
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Basic Information</h3>
          
          {/* Title */}
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Project Title *
            </label>
            <input
              type="text"
              id="title"
              name="title"
              required
              value={formData.title}
              onChange={handleInputChange}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder:text-sm"
              placeholder="Project title"
            />
          </div>

          {/* Description */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Short Description *
            </label>
            <textarea
              id="description"
              name="description"
              required
              rows={3}
              value={formData.description}
              onChange={handleInputChange}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder:text-sm"
              placeholder="Brief project description"
            />
          </div>

          {/* Featured Image */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Featured Image
            </label>
            <FeaturedImageSelector
              selectedImage={formData.featured_image}
              onImageSelect={(imageUrl) => setFormData(prev => ({ ...prev, featured_image: imageUrl }))}
            />
          </div>
        </div>

        {/* Project Metadata */}
        <div className="space-y-6 border-t border-gray-200 dark:border-gray-700 pt-8">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Project Details</h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Client */}
            <div>
              <label htmlFor="client" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Client
              </label>
              <input
                type="text"
                id="client"
                name="client"
                value={formData.client}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder:text-sm"
                placeholder="Client name"
              />
            </div>

            {/* Industry */}
            <div>
              <label htmlFor="industry" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Industry
              </label>
              <input
                type="text"
                id="industry"
                name="industry"
                value={formData.industry}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder:text-sm"
                placeholder="Industry"
              />
            </div>

            {/* Project Date */}
            <div>
              <label htmlFor="project_date" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Project Date
              </label>
              <input
                type="date"
                id="project_date"
                name="project_date"
                value={formData.project_date}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder:text-sm [&::-webkit-datetime-edit]:text-sm [&::-webkit-calendar-picker-indicator]:text-sm"
              />
            </div>
          </div>

          {/* URLs */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Project URL */}
            <div>
              <label htmlFor="project_url" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Live Site URL
              </label>
              <input
                type="url"
                id="project_url"
                name="project_url"
                value={formData.project_url}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder:text-sm"
                placeholder="https://project-url.com"
              />
            </div>

            {/* GitHub URL */}
            <div>
              <label htmlFor="github_url" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Source Code URL
              </label>
              <input
                type="url"
                id="github_url"
                name="github_url"
                value={formData.github_url}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder:text-sm"
                placeholder="https://github.com/user/repo"
              />
            </div>
          </div>

          {/* Technology Stack and Tags */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Technology Stack */}
            <div>
              <label htmlFor="tech_stack_input" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Technology Stack
              </label>
              <input
                type="text"
                id="tech_stack_input"
                value={formData.tech_stack.join(', ')}
                onChange={(e) => {
                  const value = e.target.value
                  // Don't filter out empty strings during typing to allow comma input
                  const techs = value.split(',').map(tech => tech.trim())
                  setFormData(prev => ({ ...prev, tech_stack: techs.filter(tech => tech) }))
                }}
                onInput={(e) => {
                  // Additional handler to ensure comma input works
                  const value = (e.target as HTMLInputElement).value
                  const techs = value.split(',').map(tech => tech.trim())
                  setFormData(prev => ({ ...prev, tech_stack: techs.filter(tech => tech) }))
                }}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder:text-sm"
                placeholder="React, Node.js, TypeScript"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Separate multiple technologies with commas
              </p>
            </div>

            {/* Tags */}
            <div>
              <label htmlFor="tags_input" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Tags
              </label>
              <input
                type="text"
                id="tags_input"
                value={formData.tags.join(', ')}
                onChange={(e) => {
                  const value = e.target.value
                  // Don't filter out empty strings during typing to allow comma input
                  const tags = value.split(',').map(tag => tag.trim())
                  setFormData(prev => ({ ...prev, tags: tags.filter(tag => tag) }))
                }}
                onInput={(e) => {
                  // Additional handler to ensure comma input works
                  const value = (e.target as HTMLInputElement).value
                  const tags = value.split(',').map(tag => tag.trim())
                  setFormData(prev => ({ ...prev, tags: tags.filter(tag => tag) }))
                }}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder:text-sm"
                placeholder="web development, design, frontend"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Separate multiple tags with commas
              </p>
            </div>
          </div>

          {/* Challenge and Solution */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Challenge */}
            <div>
              <label htmlFor="challenge" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                The Challenge
              </label>
              <textarea
                id="challenge"
                name="challenge"
                rows={6}
                value={formData.challenge}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder:text-sm"
                placeholder="Describe the main challenges"
              />
            </div>

            {/* Solution */}
            <div>
              <label htmlFor="solution" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                The Solution
              </label>
              <textarea
                id="solution"
                name="solution"
                rows={6}
                value={formData.solution}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder:text-sm"
                placeholder="Describe your solution"
              />
            </div>
          </div>
        </div>



        {/* Content */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-8">
          <label htmlFor="content" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Detailed Content * (Markdown supported)
          </label>
          <textarea
            id="content"
            name="content"
            required
            rows={20}
            value={formData.content}
            onChange={handleInputChange}
            className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white font-mono text-sm placeholder:text-sm"
            placeholder="Write detailed project content in Markdown"
          />
        </div>



        {/* Actions */}
        <div className="flex justify-between items-center pt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            onClick={() => router.back()}
            className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
          >
            Cancel
          </button>
          
          <div className="flex gap-3">
            <button
              type="button"
              onClick={(e) => handleSubmit(e, true)}
              disabled={saving}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-50"
            >
              Save as Draft
            </button>
            <button
              type="submit"
              disabled={saving}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              {saving ? 'Saving...' : mode === 'create' ? 'Create Project' : 'Update Project'}
            </button>
          </div>
        </div>
      </form>
    </div>
  )
}
