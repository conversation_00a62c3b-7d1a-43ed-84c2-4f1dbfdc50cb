# <PERSON> - Personal Blog & Portfolio

> **🔗 Live Site:** [ernestomelo.com](https://ernestomelo.com)

This is a **read-only code showcase** of my personal blog and portfolio website. The repository demonstrates modern web development practices and clean, minimalist design principles.

## 🚀 Live Website

Visit the live website at **[ernestomelo.com](https://ernestomelo.com)** to see the project in action.

## 📋 Project Overview

A modern, minimalist personal blog and portfolio built with cutting-edge web technologies. Features a clean design inspired by Norwegian landscapes with cool blues and grays, interactive parallax backgrounds, and seamless user experience.

### ✨ Key Features

- **Modern Tech Stack**: Next.js 14, React, TypeScript, Tailwind CSS
- **Interactive Design**: Clean minimalist interface with custom cursor and subtle animations
- **Blog System**: Markdown-based content management with reading progress, TOC, and social sharing
- **Project Portfolio**: Showcase of web development and AI automation projects
- **Contact Forms**: Integrated contact and project inquiry forms with AI-powered responses
- **Performance Optimized**: Fast loading, SEO-friendly, responsive design
- **Accessibility**: Colorblind-friendly design with proper contrast ratios

### 🎨 Design Philosophy

- **Minimalist**: Clean, uncluttered interface focusing on content
- **Norwegian-Inspired**: Cool color palette (blues/grays) with white backgrounds
- **Interactive**: Subtle animations and hover effects for enhanced UX
- **Accessible**: Designed with accessibility in mind, including colorblind users

## 🛠️ Technology Stack

### Frontend
- **Framework**: Next.js 14 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Animations**: Framer Motion
- **UI Components**: Custom components with Aceternity UI elements

### Content Management
- **Blog Posts**: Markdown files with frontmatter
- **Projects**: Markdown-based project showcase
- **Images**: Optimized with Next.js Image component

### Integrations
- **Email**: Contact forms with EmailJS integration
- **Newsletter**: Mailchimp integration
- **Analytics**: Built-in view counter
- **Deployment**: Vercel

## 📁 Project Structure

```
├── src/
│   ├── app/                 # Next.js App Router pages
│   ├── components/          # Reusable React components
│   ├── lib/                 # Utility functions and configurations
│   └── types/               # TypeScript type definitions
├── content/
│   ├── blog/                # Blog post markdown files
│   └── projects/            # Project showcase markdown files
├── public/
│   └── images/              # Static images and assets
└── styles/                  # Global styles and Tailwind config
```

## 🎯 Use Cases

This codebase serves as an example for:

- **Personal Branding**: Professional portfolio and blog setup
- **Modern Web Development**: Latest Next.js and React patterns
- **Content Management**: Markdown-based CMS implementation
- **Design Systems**: Consistent, accessible design implementation
- **Performance Optimization**: Fast, SEO-friendly website architecture

## 📝 Content Types

### Blog Posts
- Technical tutorials and insights
- Web development best practices
- AI automation guides
- Industry thoughts and opinions

### Project Portfolio
- Web development projects
- AI automation solutions
- Client work showcases
- Technical case studies

## 🔒 Repository Status

**This is a read-only showcase repository.** 

- ✅ **View**: Browse the code and learn from the implementation
- ✅ **Fork**: Create your own version for personal use
- ✅ **Study**: Understand modern web development patterns
- ❌ **Contribute**: This repository doesn't accept contributions
- ❌ **Issues**: Please visit the live site for contact

## 🌐 Connect

- **Website**: [ernestomelo.com](https://ernestomelo.com)
- **Email**: <EMAIL>
- **Projects**: <EMAIL>

---

**© 2021-2024 Ernst Romelo** | Built with ❤️ using Next.js and TypeScript
