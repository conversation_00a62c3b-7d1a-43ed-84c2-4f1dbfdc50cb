import Link from 'next/link'
import Image from 'next/image'
import { BlogPost } from '@/types'

interface RelatedPostsProps {
  posts: BlogPost[]
  currentPostSlug: string
}

export default function RelatedPosts({ posts, currentPostSlug }: RelatedPostsProps) {
  // Filter out current post and get up to 3 related posts
  const relatedPosts = posts
    .filter(post => post.slug !== currentPostSlug)
    .slice(0, 3)

  if (relatedPosts.length === 0) {
    return null
  }

  return (
    <div className="mt-16 pt-12 border-t border-gray-200 dark:border-gray-700">
      <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-8">Related Posts</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {relatedPosts.map((post) => (
          <article key={post.slug} className="group h-full">
            <div className="block h-full flex flex-col">
              {/* Featured Image */}
              <Link href={`/blog/${post.slug}`} className="block">
                <div className="relative h-48 rounded-xl overflow-hidden mb-4">
                  <Image
                    src={post.featuredImage}
                    alt={post.title}
                    width={400}
                    height={200}
                    className="object-cover object-center w-full h-full group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
              </Link>

              {/* Title */}
              <Link href={`/blog/${post.slug}`} className="no-link-style">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-200 mb-3 line-clamp-2">
                  {post.title}
                </h3>
              </Link>

              {/* Read More Link */}
              <Link
                href={`/blog/${post.slug}`}
                className="no-link-style text-orange-500 hover:text-red-600 dark:text-orange-400 dark:hover:text-orange-300 text-sm font-medium transition-colors duration-200 mt-auto"
              >
                Read more →
              </Link>
            </div>
          </article>
        ))}
      </div>
    </div>
  )
}
