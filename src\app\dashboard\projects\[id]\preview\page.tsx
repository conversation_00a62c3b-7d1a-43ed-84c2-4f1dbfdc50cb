'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { getProject } from '@/lib/firebase-operations'
import { DatabaseProject } from '@/types'
import { processMarkdownContent } from '@/lib/markdown-client'
import { 
  ArrowLeftIcon, 
  PencilIcon, 
  EyeIcon, 
  LinkIcon, 
  CodeBracketIcon,
  CalendarIcon,
  BuildingOfficeIcon,
  TagIcon
} from '@heroicons/react/24/outline'

interface PreviewProjectPageProps {
  params: {
    id: string
  }
}

export default function PreviewProjectPage({ params }: PreviewProjectPageProps) {
  const [project, setProject] = useState<DatabaseProject | null>(null)
  const [processedContent, setProcessedContent] = useState('')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadProject()
  }, [params.id])

  const loadProject = async () => {
    try {
      const projectData = await getProject(params.id)
      if (projectData) {
        setProject(projectData)
        const processed = await processMarkdownContent(projectData.content)
        setProcessedContent(processed)
      }
    } catch (error) {
      console.error('Error loading project:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!project) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Project not found
        </h2>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          The project you're looking for doesn't exist.
        </p>
        <Link
          href="/dashboard/projects"
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <ArrowLeftIcon className="w-5 h-5 mr-2" />
          Back to Projects
        </Link>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link
            href="/dashboard/projects"
            className="inline-flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
          >
            <ArrowLeftIcon className="w-5 h-5 mr-2" />
            Back to Projects
          </Link>
        </div>
        
        <div className="flex items-center space-x-3">
          <Link
            href={`/dashboard/projects/${params.id}/edit`}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <PencilIcon className="w-4 h-4 mr-2" />
            Edit Project
          </Link>
        </div>
      </div>

      {/* Preview Notice */}
      <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
        <div className="flex items-center">
          <EyeIcon className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2" />
          <div>
            <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
              Preview Mode
            </h3>
            <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
              This is how your project will appear to visitors. 
              {!project.published && ' This project is currently a draft and not visible to the public.'}
            </p>
          </div>
        </div>
      </div>

      {/* Project Preview */}
      <article className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
        {/* Featured Image */}
        {project.featured_image && (
          <div className="aspect-video w-full bg-gray-200 dark:bg-gray-700">
            <img
              src={project.featured_image}
              alt={project.title}
              className="w-full h-full object-cover"
            />
          </div>
        )}

        <div className="p-8">
          {/* Project Meta */}
          <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mb-6">
            {project.project_date && (
              <div className="flex items-center">
                <CalendarIcon className="w-4 h-4 mr-1" />
                {formatDate(project.project_date)}
              </div>
            )}
            {project.client && (
              <div className="flex items-center">
                <BuildingOfficeIcon className="w-4 h-4 mr-1" />
                {project.client}
              </div>
            )}
            {project.industry && (
              <div className="flex items-center">
                <TagIcon className="w-4 h-4 mr-1" />
                {project.industry}
              </div>
            )}
            {!project.published && (
              <span className="text-yellow-600 dark:text-yellow-400 font-medium">Draft</span>
            )}
          </div>

          {/* Title */}
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            {project.title}
          </h1>

          {/* Description */}
          <p className="text-lg text-gray-600 dark:text-gray-400 mb-8 leading-relaxed">
            {project.description}
          </p>

          {/* Project Links */}
          {(project.project_url || project.github_url) && (
            <div className="flex flex-wrap gap-4 mb-8">
              {project.project_url && (
                <a
                  href={project.project_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <LinkIcon className="w-4 h-4 mr-2" />
                  Visit Live Site
                </a>
              )}
              {project.github_url && (
                <a
                  href={project.github_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  <CodeBracketIcon className="w-4 h-4 mr-2" />
                  View Source Code
                </a>
              )}
            </div>
          )}

          {/* Technology Stack */}
          {project.tech_stack && project.tech_stack.length > 0 && (
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                Technology Used
              </h3>
              <div className="flex flex-wrap gap-2">
                {project.tech_stack.map((tech) => (
                  <span
                    key={tech}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200"
                  >
                    {tech}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Challenge & Solution */}
          {(project.challenge || project.solution) && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              {project.challenge && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                    The Challenge
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                    {project.challenge}
                  </p>
                </div>
              )}
              {project.solution && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                    The Solution
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                    {project.solution}
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Tags */}
          {project.tags && project.tags.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-8">
              {project.tags.map((tag) => (
                <span
                  key={tag}
                  className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200"
                >
                  {tag}
                </span>
              ))}
            </div>
          )}

          {/* Content */}
          <div 
            className="prose dark:prose-invert max-w-none prose-lg"
            dangerouslySetInnerHTML={{ __html: processedContent }}
          />
        </div>
      </article>
    </div>
  )
}
