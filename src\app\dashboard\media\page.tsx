'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/providers/AuthProvider'
import { getUploadedFiles, uploadFile, deleteFile } from '@/lib/firebase-operations'
import { UploadedFile } from '@/types'
import {
  PhotoIcon,
  CloudArrowUpIcon,
  TrashIcon,
  EyeIcon,
  DocumentIcon,
  PlusIcon,
  ClipboardDocumentIcon,
  CheckIcon
} from '@heroicons/react/24/outline'

export default function MediaPage() {
  const { user } = useAuth()
  const [files, setFiles] = useState<UploadedFile[]>([])
  const [loading, setLoading] = useState(true)
  const [uploading, setUploading] = useState(false)
  const [deleting, setDeleting] = useState<string | null>(null)
  const [selectedFile, setSelectedFile] = useState<UploadedFile | null>(null)
  const [copiedFileId, setCopiedFileId] = useState<string | null>(null)

  useEffect(() => {
    if (user) {
      loadFiles()
    }
  }, [user])

  const loadFiles = async () => {
    if (!user) return
    
    try {
      const userFiles = await getUploadedFiles(user.uid)
      setFiles(userFiles)
    } catch (error) {
      console.error('Error loading files:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file || !user) return

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      alert('File size must be less than 10MB')
      return
    }

    setUploading(true)
    try {
      const uploadedFile = await uploadFile(file, user.uid, 'media')
      setFiles([uploadedFile, ...files])
    } catch (error) {
      console.error('Error uploading file:', error)
      alert('Failed to upload file')
    } finally {
      setUploading(false)
    }
  }

  const handleDelete = async (file: UploadedFile) => {
    if (!confirm(`Are you sure you want to delete "${file.original_name}"?`)) return

    setDeleting(file.id)
    try {
      await deleteFile(file.id, file.file_path)
      setFiles(files.filter(f => f.id !== file.id))
      if (selectedFile?.id === file.id) {
        setSelectedFile(null)
      }
    } catch (error) {
      console.error('Error deleting file:', error)
      alert('Failed to delete file')
    } finally {
      setDeleting(null)
    }
  }

  const handleCopyLink = async (file: UploadedFile, e: React.MouseEvent) => {
    e.stopPropagation()
    try {
      await navigator.clipboard.writeText(file.download_url)
      setCopiedFileId(file.id)
      setTimeout(() => setCopiedFileId(null), 2000)
    } catch (error) {
      console.error('Failed to copy link:', error)
      alert('Failed to copy link to clipboard')
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const isImage = (mimeType: string) => mimeType.startsWith('image/')

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Media Library
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage your uploaded images and files.
          </p>
        </div>
        
        <div>
          <label htmlFor="file-upload" className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors cursor-pointer">
            <PlusIcon className="w-5 h-5 mr-2" />
            Upload File
          </label>
          <input
            id="file-upload"
            type="file"
            onChange={handleFileUpload}
            disabled={uploading}
            className="hidden"
            accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt"
          />
        </div>
      </div>

      {/* Upload Status */}
      {uploading && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3"></div>
            <span className="text-blue-800 dark:text-blue-200">Uploading file...</span>
          </div>
        </div>
      )}

      {/* Files Grid */}
      {files.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
            <PhotoIcon className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No files uploaded yet
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Upload your first file to get started.
          </p>
          <label htmlFor="file-upload-empty" className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors cursor-pointer">
            <CloudArrowUpIcon className="w-5 h-5 mr-2" />
            Upload Your First File
          </label>
          <input
            id="file-upload-empty"
            type="file"
            onChange={handleFileUpload}
            disabled={uploading}
            className="hidden"
            accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt"
          />
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Files Grid */}
          <div className="lg:col-span-2">
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
              {files.map((file) => (
                <div
                  key={file.id}
                  className={`relative group border-2 rounded-lg overflow-hidden cursor-pointer transition-all ${
                    selectedFile?.id === file.id
                      ? 'border-blue-500 ring-2 ring-blue-200 dark:ring-blue-800'
                      : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                  }`}
                  onClick={() => setSelectedFile(file)}
                >
                  {/* File Preview */}
                  <div className="aspect-square bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
                    {isImage(file.mime_type) ? (
                      <img
                        src={file.download_url}
                        alt={file.original_name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <DocumentIcon className="w-12 h-12 text-gray-400" />
                    )}
                  </div>

                  {/* File Info Overlay */}
                  <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white text-xs p-2">
                    <div className="truncate font-medium">{file.original_name}</div>
                    <div className="text-gray-300">{formatFileSize(file.file_size)}</div>
                  </div>

                  {/* Actions Overlay */}
                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <div className="flex space-x-1">
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation()
                          window.open(file.download_url, '_blank')
                        }}
                        className="p-1 bg-black bg-opacity-50 text-white rounded hover:bg-opacity-75"
                        title="View"
                      >
                        <EyeIcon className="w-4 h-4" />
                      </button>
                      <button
                        type="button"
                        onClick={(e) => handleCopyLink(file, e)}
                        className="p-1 bg-black bg-opacity-50 text-white rounded hover:bg-opacity-75"
                        title="Copy link"
                      >
                        {copiedFileId === file.id ? (
                          <CheckIcon className="w-4 h-4 text-green-400" />
                        ) : (
                          <ClipboardDocumentIcon className="w-4 h-4" />
                        )}
                      </button>
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDelete(file)
                        }}
                        disabled={deleting === file.id}
                        className="p-1 bg-black bg-opacity-50 text-white rounded hover:bg-opacity-75 disabled:opacity-50"
                        title="Delete"
                      >
                        {deleting === file.id ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        ) : (
                          <TrashIcon className="w-4 h-4" />
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* File Details Sidebar */}
          <div className="lg:col-span-1">
            {selectedFile ? (
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 sticky top-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  File Details
                </h3>
                
                {/* File Preview */}
                <div className="mb-4">
                  {isImage(selectedFile.mime_type) ? (
                    <img
                      src={selectedFile.download_url}
                      alt={selectedFile.original_name}
                      className="w-full h-48 object-cover rounded-lg"
                    />
                  ) : (
                    <div className="w-full h-48 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                      <DocumentIcon className="w-16 h-16 text-gray-400" />
                    </div>
                  )}
                </div>

                {/* File Info */}
                <div className="space-y-3 text-sm">
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Name:</span>
                    <p className="text-gray-600 dark:text-gray-400 break-words">{selectedFile.original_name}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Size:</span>
                    <p className="text-gray-600 dark:text-gray-400">{formatFileSize(selectedFile.file_size)}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Type:</span>
                    <p className="text-gray-600 dark:text-gray-400">{selectedFile.mime_type}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Uploaded:</span>
                    <p className="text-gray-600 dark:text-gray-400">{formatDate(selectedFile.created_at)}</p>
                  </div>
                </div>

                {/* Actions */}
                <div className="mt-6 space-y-2">
                  <button
                    onClick={() => window.open(selectedFile.download_url, '_blank')}
                    className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    View Full Size
                  </button>
                  <button
                    onClick={() => navigator.clipboard.writeText(selectedFile.download_url)}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    Copy URL
                  </button>
                  <button
                    onClick={() => handleDelete(selectedFile)}
                    disabled={deleting === selectedFile.id}
                    className="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
                  >
                    {deleting === selectedFile.id ? 'Deleting...' : 'Delete File'}
                  </button>
                </div>
              </div>
            ) : (
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 text-center">
                <PhotoIcon className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                <p className="text-gray-600 dark:text-gray-400">
                  Select a file to view details
                </p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
