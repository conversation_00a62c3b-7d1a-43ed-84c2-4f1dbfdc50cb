'use client'

export default function BlogGridSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 auto-rows-fr">
      {Array.from({ length: 6 }).map((_, index) => (
        <div
          key={index}
          className="bg-white rounded-xl shadow-sm border border-neutral-200 h-full flex flex-col animate-pulse"
        >
          {/* Featured Image Skeleton */}
          <div className="h-48 bg-gray-200 rounded-t-xl flex-shrink-0" />

          {/* Content Skeleton */}
          <div className="p-6 flex flex-col flex-grow">
            {/* Date and Read Time Skeleton */}
            <div className="flex items-center mb-3">
              <div className="h-4 bg-gray-200 rounded w-24" />
              <div className="mx-2 w-1 h-1 bg-gray-200 rounded-full" />
              <div className="h-4 bg-gray-200 rounded w-16" />
            </div>

            {/* Title Skeleton */}
            <div className="mb-3">
              <div className="h-6 bg-gray-200 rounded w-full mb-2" />
              <div className="h-6 bg-gray-200 rounded w-3/4" />
            </div>

            {/* Excerpt Skeleton */}
            <div className="mb-4 flex-grow">
              <div className="h-4 bg-gray-200 rounded w-full mb-2" />
              <div className="h-4 bg-gray-200 rounded w-full mb-2" />
              <div className="h-4 bg-gray-200 rounded w-2/3" />
            </div>

            {/* Categories Skeleton */}
            <div className="mt-auto">
              <div className="flex flex-wrap gap-2 mb-4">
                <div className="h-6 bg-gray-200 rounded-full w-20" />
                <div className="h-6 bg-gray-200 rounded-full w-16" />
              </div>

              {/* Read More Skeleton */}
              <div className="flex items-center">
                <div className="h-4 bg-gray-200 rounded w-20" />
                <div className="w-4 h-4 ml-1 bg-gray-200 rounded" />
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
