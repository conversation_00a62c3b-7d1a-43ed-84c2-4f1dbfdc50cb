'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/providers/AuthProvider'
import { getUploadedFiles, deleteFile } from '@/lib/firebase-operations'
import { UploadedFile } from '@/types'
import { TrashIcon, CheckIcon, LinkIcon, PhotoIcon, ClipboardDocumentIcon } from '@heroicons/react/24/outline'

interface MediaLibraryProps {
  onSelect?: (file: UploadedFile & { url: string }) => void
  selectedFileId?: string
  showDeleteButton?: boolean
  showHoverActions?: boolean
  onSelectAsFeatured?: (file: UploadedFile & { url: string }) => void
  className?: string
}

export default function MediaLibrary({
  onSelect,
  selectedFileId,
  showDeleteButton = false,
  showHoverActions = false,
  onSelectAsFeatured,
  className = ''
}: MediaLibraryProps) {
  const { user } = useAuth()
  const [files, setFiles] = useState<UploadedFile[]>([])
  const [loading, setLoading] = useState(true)
  const [deleting, setDeleting] = useState<string | null>(null)
  const [copiedFileId, setCopiedFileId] = useState<string | null>(null)

  useEffect(() => {
    if (user) {
      loadFiles()
    }
  }, [user])

  const loadFiles = async () => {
    if (!user) return

    try {
      const uploadedFiles = await getUploadedFiles(user.uid)
      setFiles(uploadedFiles)
    } catch (error) {
      console.error('Error loading files:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteFile = async (file: UploadedFile) => {
    if (!confirm('Are you sure you want to delete this file?')) return

    setDeleting(file.id)
    try {
      await deleteFile(file.id, file.file_path)
      // Update local state
      setFiles(files.filter(f => f.id !== file.id))
    } catch (error) {
      console.error('Error deleting file:', error)
      alert('Failed to delete file')
    } finally {
      setDeleting(null)
    }
  }

  const handleCopyLink = async (file: UploadedFile, e: React.MouseEvent) => {
    e.stopPropagation()
    try {
      await navigator.clipboard.writeText(file.download_url)
      setCopiedFileId(file.id)
      setTimeout(() => setCopiedFileId(null), 2000)
    } catch (error) {
      console.error('Failed to copy link:', error)
      alert('Failed to copy link to clipboard')
    }
  }

  const handleSelectAsFeatured = (file: UploadedFile, e: React.MouseEvent) => {
    e.stopPropagation()
    const fileWithUrl = { ...file, url: file.download_url }
    onSelectAsFeatured?.(fileWithUrl)
  }

  const getFileUrl = (file: UploadedFile) => {
    return file.download_url
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  if (loading) {
    return (
      <div className={`${className} flex justify-center items-center py-8`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (files.length === 0) {
    return (
      <div className={`${className} text-center py-8`}>
        <p className="text-gray-500 dark:text-gray-400">No files uploaded yet.</p>
      </div>
    )
  }

  return (
    <div className={className}>
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {files.map((file) => {
          const url = getFileUrl(file)
          const isSelected = selectedFileId === file.id
          
          return (
            <div
              key={file.id}
              className={`
                relative group border-2 rounded-lg overflow-hidden cursor-pointer transition-all
                ${isSelected 
                  ? 'border-blue-500 ring-2 ring-blue-200 dark:ring-blue-800' 
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                }
              `}
              onClick={() => onSelect?.({ ...file, url })}
            >
              {/* Image */}
              <div className="aspect-square bg-gray-100 dark:bg-gray-800">
                <img
                  src={url}
                  alt={file.original_name}
                  className="w-full h-full object-cover"
                  loading="lazy"
                />
              </div>

              {/* Overlay */}
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all">
                {/* Selected indicator */}
                {isSelected && (
                  <div className="absolute top-2 right-2 bg-blue-500 text-white rounded-full p-1">
                    <CheckIcon className="h-4 w-4" />
                  </div>
                )}

                {/* Hover Actions */}
                {showHoverActions && (
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                    <div className="flex space-x-2">
                      {/* Copy Link Button */}
                      <button
                        type="button"
                        onClick={(e) => handleCopyLink(file, e)}
                        className="bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full p-2 shadow-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                        title="Copy image link"
                      >
                        {copiedFileId === file.id ? (
                          <CheckIcon className="h-4 w-4 text-green-600" />
                        ) : (
                          <ClipboardDocumentIcon className="h-4 w-4" />
                        )}
                      </button>

                      {/* Select as Featured Image Button */}
                      {onSelectAsFeatured && (
                        <button
                          type="button"
                          onClick={(e) => handleSelectAsFeatured(file, e)}
                          className="bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full p-2 shadow-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                          title="Select as featured image"
                        >
                          <PhotoIcon className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </div>
                )}

                {/* Delete button */}
                {showDeleteButton && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleDeleteFile(file)
                    }}
                    disabled={deleting === file.id}
                    className="absolute top-2 left-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600 disabled:opacity-50"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                )}
              </div>

              {/* File info */}
              <div className="p-2 bg-white dark:bg-gray-800">
                <p className="text-xs text-gray-600 dark:text-gray-400 truncate" title={file.original_name}>
                  {file.original_name}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-500">
                  {formatFileSize(file.file_size)}
                </p>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
