'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/providers/AuthProvider'
import { getBlogPosts, getProjects, getUploadedFiles } from '@/lib/firebase-operations'
import {
  DocumentTextIcon,
  RocketLaunchIcon,
  PhotoIcon,
  EyeIcon,
  CalendarIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline'

export default function AnalyticsPage() {
  const { user } = useAuth()
  const [stats, setStats] = useState({
    totalPosts: 0,
    publishedPosts: 0,
    draftPosts: 0,
    totalProjects: 0,
    publishedProjects: 0,
    draftProjects: 0,
    totalFiles: 0,
    totalFileSize: 0,
  })
  const [recentActivity, setRecentActivity] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user) {
      loadAnalytics()
    }
  }, [user])

  const loadAnalytics = async () => {
    if (!user) return

    try {
      // Load posts
      const posts = await getBlogPosts(user.uid)
      const publishedPosts = posts.filter(post => post.published)
      const draftPosts = posts.filter(post => !post.published)

      // Load projects
      const projects = await getProjects(user.uid)
      const publishedProjects = projects.filter(project => project.published)
      const draftProjects = projects.filter(project => !project.published)

      // Load files
      const files = await getUploadedFiles(user.uid)
      const totalFileSize = files.reduce((total, file) => total + file.file_size, 0)

      // Combine recent activity
      const allItems = [
        ...posts.map(post => ({
          type: 'post',
          title: post.title,
          status: post.published ? 'published' : 'draft',
          date: post.updated_at,
          id: post.id
        })),
        ...projects.map(project => ({
          type: 'project',
          title: project.title,
          status: project.published ? 'published' : 'draft',
          date: project.updated_at,
          id: project.id
        })),
        ...files.map(file => ({
          type: 'file',
          title: file.original_name,
          status: 'uploaded',
          date: file.created_at,
          id: file.id
        }))
      ].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()).slice(0, 10)

      setStats({
        totalPosts: posts.length,
        publishedPosts: publishedPosts.length,
        draftPosts: draftPosts.length,
        totalProjects: projects.length,
        publishedProjects: publishedProjects.length,
        draftProjects: draftProjects.length,
        totalFiles: files.length,
        totalFileSize,
      })

      setRecentActivity(allItems)
    } catch (error) {
      console.error('Error loading analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'post':
        return DocumentTextIcon
      case 'project':
        return RocketLaunchIcon
      case 'file':
        return PhotoIcon
      default:
        return DocumentTextIcon
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'text-green-600 dark:text-green-400'
      case 'draft':
        return 'text-yellow-600 dark:text-yellow-400'
      case 'uploaded':
        return 'text-blue-600 dark:text-blue-400'
      default:
        return 'text-gray-600 dark:text-gray-400'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Analytics
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Overview of your content and activity.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Blog Posts */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <DocumentTextIcon className="w-8 h-8 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Blog Posts</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">{stats.totalPosts}</p>
              <p className="text-xs text-gray-500 dark:text-gray-500">
                {stats.publishedPosts} published, {stats.draftPosts} drafts
              </p>
            </div>
          </div>
        </div>

        {/* Projects */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <RocketLaunchIcon className="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Projects</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">{stats.totalProjects}</p>
              <p className="text-xs text-gray-500 dark:text-gray-500">
                {stats.publishedProjects} published, {stats.draftProjects} drafts
              </p>
            </div>
          </div>
        </div>

        {/* Media Files */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <PhotoIcon className="w-8 h-8 text-purple-600 dark:text-purple-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Media Files</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">{stats.totalFiles}</p>
              <p className="text-xs text-gray-500 dark:text-gray-500">
                {formatFileSize(stats.totalFileSize)} total
              </p>
            </div>
          </div>
        </div>

        {/* Total Content */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ChartBarIcon className="w-8 h-8 text-orange-600 dark:text-orange-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Items</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {stats.totalPosts + stats.totalProjects + stats.totalFiles}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-500">
                All content items
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Recent Activity</h2>
        </div>
        <div className="p-6">
          {recentActivity.length === 0 ? (
            <div className="text-center py-8">
              <CalendarIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400">No recent activity</p>
            </div>
          ) : (
            <div className="space-y-4">
              {recentActivity.map((item, index) => {
                const Icon = getActivityIcon(item.type)
                return (
                  <div key={`${item.type}-${item.id}-${index}`} className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <Icon className="w-5 h-5 text-gray-400" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {item.title}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        <span className="capitalize">{item.type}</span>
                        <span className="mx-1">•</span>
                        <span className={getStatusColor(item.status)}>{item.status}</span>
                      </p>
                    </div>
                    <div className="flex-shrink-0 text-sm text-gray-500 dark:text-gray-400">
                      {formatDate(item.date)}
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <a
            href="/dashboard/posts/new"
            className="flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <DocumentTextIcon className="w-5 h-5 mr-2 text-blue-600 dark:text-blue-400" />
            <span className="text-gray-700 dark:text-gray-300">New Blog Post</span>
          </a>
          <a
            href="/dashboard/projects/new"
            className="flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <RocketLaunchIcon className="w-5 h-5 mr-2 text-green-600 dark:text-green-400" />
            <span className="text-gray-700 dark:text-gray-300">New Project</span>
          </a>
          <a
            href="/dashboard/media"
            className="flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <PhotoIcon className="w-5 h-5 mr-2 text-purple-600 dark:text-purple-400" />
            <span className="text-gray-700 dark:text-gray-300">Upload Media</span>
          </a>
        </div>
      </div>
    </div>
  )
}
