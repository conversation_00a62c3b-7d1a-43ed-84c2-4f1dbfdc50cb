'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Search, Moon, Sun } from 'lucide-react'
import { useTheme } from '@/components/providers/ThemeProvider'
import SearchModal from '@/components/SearchModal'

export default function LeftSidebar() {
  const { theme, setTheme } = useTheme()
  const [isSearchOpen, setIsSearchOpen] = useState(false)

  const getCurrentTheme = () => {
    if (theme === 'system') {
      if (typeof window !== 'undefined') {
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
      }
      return 'light'
    }
    return theme
  }

  const openSearch = () => {
    setIsSearchOpen(true)
  }

  return (
    <>
      {/* Left Sidebar */}
      <div className="fixed left-4 top-1/2 -translate-y-1/2 z-40 hidden lg:block">
        <div className="flex flex-col items-center space-y-8">
          {/* Dark/Light Mode Toggle - Sleek Sliding Design */}
          <motion.div
            className="relative bg-gray-200 dark:bg-gray-700 rounded-full w-12 h-32"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            {/* Sliding Oblong Background - fills exactly half the container */}
            <motion.div
              className="absolute inset-0 w-full h-16 bg-white dark:bg-gray-800 rounded-full shadow-md"
              animate={{
                y: getCurrentTheme() === 'dark' ? 0 : '100%'
              }}
              transition={{ type: "spring", stiffness: 400, damping: 35 }}
            />

            {/* Dark Mode Button */}
            <motion.button
              onClick={() => setTheme('dark')}
              className="relative z-10 flex flex-col items-center justify-center w-full h-16 transition-all duration-300"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Moon className={`w-4 h-4 mb-1 ${getCurrentTheme() === 'dark' ? 'text-gray-800 dark:text-white' : 'text-gray-400'}`} />
              <span
                className={`text-xs font-medium tracking-wider ${getCurrentTheme() === 'dark' ? 'text-gray-800 dark:text-white' : 'text-gray-400'}`}
                style={{ writingMode: 'vertical-rl', textOrientation: 'mixed' }}
              >
                Dark
              </span>
            </motion.button>

            {/* Light Mode Button */}
            <motion.button
              onClick={() => setTheme('light')}
              className="relative z-10 flex flex-col items-center justify-center w-full h-16 transition-all duration-300"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Sun className={`w-4 h-4 mb-1 ${getCurrentTheme() === 'light' ? 'text-gray-800 dark:text-white' : 'text-gray-400'}`} />
              <span
                className={`text-xs font-medium tracking-wider ${getCurrentTheme() === 'light' ? 'text-gray-800 dark:text-white' : 'text-gray-400'}`}
                style={{ writingMode: 'vertical-rl', textOrientation: 'mixed' }}
              >
                Light
              </span>
            </motion.button>
          </motion.div>

          {/* Scroll to Top Button */}
          <motion.div
            className="flex flex-col items-center"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            <motion.button
              onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
              className="group relative flex flex-col items-center justify-center py-2 text-gray-800 dark:text-gray-200 hover:text-blue-600 dark:hover:text-blue-400 transition-all duration-300"
              title="Scroll to top"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {/* Vertical Text */}
              <span
                className="text-xs font-medium tracking-wider mb-2"
                style={{ writingMode: 'vertical-rl', textOrientation: 'mixed' }}
              >
                Scroll to top
              </span>

              {/* Decorative Vertical Line */}
              <div className="w-0.5 h-8 bg-gray-400 dark:bg-gray-500 group-hover:bg-blue-600 dark:group-hover:bg-blue-400 transition-colors duration-300 rounded-full" />
            </motion.button>
          </motion.div>
        </div>
      </div>

      {/* Search Button - Bottom Left Corner */}
      <motion.button
        onClick={openSearch}
        className="fixed bottom-6 left-6 w-12 h-12 bg-gray-800 dark:bg-gray-200 hover:bg-gray-700 dark:hover:bg-gray-300 rounded-full flex items-center justify-center transition-all duration-300 shadow-lg z-40 hidden lg:flex"
        title="Search blog posts and projects"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      >
        <Search className="w-5 h-5 text-white dark:text-gray-800" />
      </motion.button>

      {/* Search Modal */}
      <SearchModal isOpen={isSearchOpen} onClose={() => setIsSearchOpen(false)} />
    </>
  )
}
