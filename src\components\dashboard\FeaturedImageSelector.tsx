'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/providers/AuthProvider'
import { uploadFile, getUploadedFiles } from '@/lib/firebase-operations'
import { UploadedFile } from '@/types'
import {
  PhotoIcon,
  CloudArrowUpIcon,
  LinkIcon,
  CheckIcon,
  ClipboardDocumentIcon
} from '@heroicons/react/24/outline'

interface FeaturedImageSelectorProps {
  selectedImage: string
  onImageSelect: (imageUrl: string) => void
}

export default function FeaturedImageSelector({ selectedImage, onImageSelect }: FeaturedImageSelectorProps) {
  const { user } = useAuth()
  const [activeTab, setActiveTab] = useState<'upload' | 'library' | 'url'>('upload')
  const [files, setFiles] = useState<UploadedFile[]>([])
  const [loading, setLoading] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [urlInput, setUrlInput] = useState('')
  const [isDragOver, setIsDragOver] = useState(false)
  const [copiedFileId, setCopiedFileId] = useState<string | null>(null)

  useEffect(() => {
    if (activeTab === 'library' && user) {
      loadFiles()
    }
  }, [activeTab, user])

  const loadFiles = async () => {
    if (!user) return
    
    setLoading(true)
    try {
      const userFiles = await getUploadedFiles(user.uid)
      // Filter for image files only
      const imageFiles = userFiles.filter(file => 
        file.mime_type.startsWith('image/')
      )
      setFiles(imageFiles)
    } catch (error) {
      console.error('Error loading files:', error)
    } finally {
      setLoading(false)
    }
  }

  const validateAndUploadFile = async (file: File) => {
    if (!file || !user) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file')
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('File size must be less than 5MB')
      return
    }

    setUploading(true)
    try {
      const uploadedFile = await uploadFile(file, user.uid, 'blog-images')
      onImageSelect(uploadedFile.download_url)

      // Refresh the library if we're on that tab
      if (activeTab === 'library') {
        loadFiles()
      }
    } catch (error) {
      console.error('Error uploading file:', error)
      alert('Failed to upload image')
    } finally {
      setUploading(false)
    }
  }

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      await validateAndUploadFile(file)
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(false)
  }

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(false)

    const files = Array.from(e.dataTransfer.files)
    const imageFile = files.find(file => file.type.startsWith('image/'))

    if (imageFile) {
      await validateAndUploadFile(imageFile)
    } else if (files.length > 0) {
      alert('Please drop an image file')
    }
  }

  const handleUrlSubmit = () => {
    if (urlInput.trim()) {
      onImageSelect(urlInput.trim())
      setUrlInput('')
    }
  }

  const handleCopyLink = async (file: UploadedFile, e: React.MouseEvent) => {
    e.stopPropagation()
    try {
      await navigator.clipboard.writeText(file.download_url)
      setCopiedFileId(file.id)
      setTimeout(() => setCopiedFileId(null), 2000)
    } catch (error) {
      console.error('Failed to copy link:', error)
      alert('Failed to copy link to clipboard')
    }
  }

  const handleSelectAsFeatured = (file: UploadedFile, e: React.MouseEvent) => {
    e.stopPropagation()
    onImageSelect(file.download_url)
  }

  const tabs = [
    { id: 'upload', name: 'Upload New', icon: CloudArrowUpIcon },
    { id: 'library', name: 'Media Library', icon: PhotoIcon },
    { id: 'url', name: 'From URL', icon: LinkIcon },
  ]

  return (
    <div className="border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
      {/* Selected Image Preview */}
      {selectedImage && (
        <div className="p-4 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Selected Image:
            </span>
            <button
              type="button"
              onClick={() => onImageSelect('')}
              className="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200"
            >
              Remove
            </button>
          </div>
          <div className="relative w-full h-32 bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden">
            <img
              src={selectedImage}
              alt="Selected featured image"
              className="w-full h-full object-cover"
            />
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="flex">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              type="button"
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex-1 flex items-center justify-center px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
            >
              <tab.icon className="w-4 h-4 mr-2" />
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="p-4">
        {activeTab === 'upload' && (
          <div>
            <div
              className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                isDragOver
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-300 dark:border-gray-600'
              } ${uploading ? 'opacity-50 pointer-events-none' : ''}`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              <CloudArrowUpIcon className={`w-12 h-12 mx-auto mb-4 ${
                isDragOver ? 'text-blue-500' : 'text-gray-400'
              }`} />
              <div className="mb-4">
                <label htmlFor="file-upload" className="cursor-pointer">
                  <span className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 font-medium">
                    Click to upload
                  </span>
                  <span className="text-gray-500 dark:text-gray-400"> or drag and drop</span>
                </label>
                <input
                  id="file-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  disabled={uploading}
                  className="hidden"
                />
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                PNG, JPG, GIF up to 5MB
              </p>
              {uploading && (
                <div className="mt-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">Uploading...</p>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'library' && (
          <div>
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              </div>
            ) : files.length === 0 ? (
              <div className="text-center py-8">
                <PhotoIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 dark:text-gray-400">No images in your library yet.</p>
                <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
                  Upload some images to get started.
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                {files.map((file) => (
                  <div
                    key={file.id}
                    className={`relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all ${
                      selectedImage === file.download_url
                        ? 'border-blue-500 ring-2 ring-blue-200 dark:ring-blue-800'
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                    }`}
                    onClick={() => onImageSelect(file.download_url)}
                  >
                    <div className="aspect-square bg-gray-100 dark:bg-gray-800">
                      <img
                        src={file.download_url}
                        alt={file.original_name}
                        className="w-full h-full object-cover"
                      />
                    </div>

                    {/* Overlay with hover actions */}
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all">
                      {/* Hover Actions */}
                      <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                        <div className="flex space-x-2">
                          {/* Copy Link Button */}
                          <button
                            type="button"
                            onClick={(e) => handleCopyLink(file, e)}
                            className="bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full p-2 shadow-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                            title="Copy image link"
                          >
                            {copiedFileId === file.id ? (
                              <CheckIcon className="h-4 w-4 text-green-600" />
                            ) : (
                              <ClipboardDocumentIcon className="h-4 w-4" />
                            )}
                          </button>

                          {/* Select as Featured Image Button */}
                          <button
                            type="button"
                            onClick={(e) => handleSelectAsFeatured(file, e)}
                            className="bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full p-2 shadow-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                            title="Select as featured image"
                          >
                            <PhotoIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>

                    {selectedImage === file.download_url && (
                      <div className="absolute top-2 right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                        <CheckIcon className="w-4 h-4 text-white" />
                      </div>
                    )}
                    <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-2 truncate">
                      {file.original_name}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'url' && (
          <div>
            <div className="space-y-4">
              <div>
                <label htmlFor="image-url" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Image URL
                </label>
                <input
                  type="url"
                  id="image-url"
                  value={urlInput}
                  onChange={(e) => setUrlInput(e.target.value)}
                  placeholder="https://example.com/image.jpg"
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>
              <button
                type="button"
                onClick={handleUrlSubmit}
                disabled={!urlInput.trim()}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Use This Image
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
