'use client'

import Link from 'next/link'
import ProjectForm from '@/components/dashboard/ProjectForm'
import { ArrowLeftIcon } from '@heroicons/react/24/outline'

interface EditProjectPageProps {
  params: {
    id: string
  }
}

export default function EditProjectPage({ params }: EditProjectPageProps) {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link
            href="/dashboard/projects"
            className="inline-flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
          >
            <ArrowLeftIcon className="w-5 h-5 mr-2" />
            Back to Projects
          </Link>
        </div>
      </div>

      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Edit Project
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Make changes to your project showcase.
        </p>
      </div>

      {/* Form */}
      <ProjectForm mode="edit" projectId={params.id} />
    </div>
  )
}
