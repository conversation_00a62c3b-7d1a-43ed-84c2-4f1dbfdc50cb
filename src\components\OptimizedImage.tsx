'use client'

import Image from 'next/image'

interface OptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  priority?: boolean
  placeholder?: 'blur' | 'empty'
  blurDataURL?: string
}

export default function OptimizedImage({
  src,
  alt,
  width,
  height,
  className,
  priority,
  placeholder,
  blurDataURL,
}: OptimizedImageProps) {
  // If it's a Firebase Storage URL or Supabase Storage URL, use regular img tag to avoid Vercel optimization
  if (src.includes('firebasestorage.googleapis.com') || src.includes('supabase.co')) {
    return (
      <img
        src={src}
        alt={alt}
        width={width}
        height={height}
        className={className}
        loading={priority ? 'eager' : 'lazy'}
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover',
          objectPosition: 'center',
        }}
      />
    )
  }

  // For other images, use Next.js Image component
  return (
    <Image
      src={src}
      alt={alt}
      width={width || 400}
      height={height || 300}
      className={className}
      priority={priority}
      placeholder={placeholder}
      blurDataURL={blurDataURL}
    />
  )
}
