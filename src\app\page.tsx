'use client'

import { useEffect, useState } from 'react'
import { getBlogPosts } from '@/lib/firebase-operations'
import { processMarkdownContent } from '@/lib/markdown-client'
import BlogGridWithFilter from '@/components/BlogGridWithFilter'
import { BlogPost, DatabaseBlogPost } from '@/types'

// Convert Firebase posts to BlogPost format
async function convertFirebasePostToBlogPost(firebasePost: DatabaseBlogPost): Promise<BlogPost> {
  // Process markdown content
  const processedContent = await processMarkdownContent(firebasePost.content)

  return {
    slug: firebasePost.slug,
    title: firebasePost.title,
    excerpt: firebasePost.excerpt,
    date: firebasePost.scheduled_for || firebasePost.created_at,
    featuredImage: firebasePost.featured_image || '/images/blog/default.png',
    content: processedContent,
    readTime: firebasePost.reading_time || 5,
    tags: firebasePost.tags || [],
    author: '<PERSON>',
    categories: firebasePost.categories || [],
  }
}

export default function Home() {
  const [posts, setPosts] = useState<BlogPost[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function loadPosts() {
      try {
        // Get posts from Firebase only
        const firebasePosts = await getBlogPosts()

        // Convert Firebase posts to BlogPost format and filter published posts
        const publishedPosts = firebasePosts.filter(post => post.published)
        const convertedPosts = await Promise.all(
          publishedPosts.map(post => convertFirebasePostToBlogPost(post))
        )
        const sortedPosts = convertedPosts.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())

        setPosts(sortedPosts)
      } catch (error) {
        console.error('Error loading posts:', error)
        setPosts([])
      } finally {
        setLoading(false)
      }
    }

    loadPosts()
  }, [])

  return (
    <div className="relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        <div className="text-center py-20 mb-16">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            <span className="bg-gradient-to-r from-blue-500 via-blue-400 to-orange-400 dark:from-blue-400 dark:via-cyan-300 dark:to-orange-300 bg-clip-text text-transparent drop-shadow-sm">
              AI Automation, Website and App Development Expert Solutions
            </span>
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Thoughts on web development, AI automation, and the intersection of technology and creativity.
          </p>
        </div>

        {/* Blog Grid with Filter */}
        <div className="py-12 mb-20">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <BlogGridWithFilter posts={posts} />
          )}
        </div>
      </div>
    </div>
  )
}
