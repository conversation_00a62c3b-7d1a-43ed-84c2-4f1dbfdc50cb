'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { motion } from 'framer-motion'


export default function Header() {
  const [isContactModalOpen, setIsContactModalOpen] = useState(false)

  const handleContactClick = () => {
    setIsContactModalOpen(true)
    // Dispatch custom event to open contact modal
    window.dispatchEvent(new CustomEvent('openContactModal'))
  }

  return (
    <motion.header
      className="fixed top-0 left-0 right-0 backdrop-blur-sm bg-white/10 dark:bg-gray-900/10 z-50"
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo/Avatar Section */}
          <Link href="/" className="flex items-center group">
            <div className="relative w-10 h-10 rounded-full overflow-hidden ring-2 ring-neutral-300 dark:ring-neutral-600 group-hover:ring-primary-300 dark:group-hover:ring-primary-400 transition-all duration-300">
              <Image
                src="/images/avatar.jpg"
                alt="Ernst Romelo"
                width={40}
                height={40}
                className="object-cover rounded-full"
                priority
              />
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link
              href="/"
              className="no-link-style text-gray-700 dark:text-gray-100 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-300 font-medium"
            >
              Blog
            </Link>
            <Link
              href="/projects"
              className="no-link-style text-gray-700 dark:text-gray-100 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-300 font-medium"
            >
              Projects
            </Link>

            <button
              onClick={handleContactClick}
              className="px-4 py-2 bg-primary-50 hover:bg-primary-100 dark:bg-gray-700 dark:hover:bg-gray-600 text-primary-600 hover:text-primary-700 dark:text-gray-100 dark:hover:text-white transition-all duration-300 font-medium rounded-lg shadow-sm hover:shadow-md"
            >
              <EMAIL>
            </button>
          </nav>

          {/* Mobile Navigation - Icons Only */}
          <div className="md:hidden flex items-center space-x-4">
            <Link
              href="/"
              className="flex items-center justify-center w-10 h-10 text-gray-600 dark:text-gray-100 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-primary-50 dark:hover:bg-gray-700 rounded-lg transition-all duration-300"
              title="Blog"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
              </svg>
            </Link>
            <Link
              href="/projects"
              className="flex items-center justify-center w-10 h-10 text-gray-600 dark:text-gray-100 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-primary-50 dark:hover:bg-gray-700 rounded-lg transition-all duration-300"
              title="Projects"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            </Link>

            <button
              onClick={handleContactClick}
              className="flex items-center justify-center w-10 h-10 bg-primary-50 hover:bg-primary-100 dark:bg-gray-700 dark:hover:bg-gray-600 text-primary-600 hover:text-primary-700 dark:text-gray-100 dark:hover:text-white transition-all duration-300 rounded-lg shadow-sm hover:shadow-md"
              title="Contact - <EMAIL>"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </button>
          </div>
        </div>
      </div>


    </motion.header>
  )
}
