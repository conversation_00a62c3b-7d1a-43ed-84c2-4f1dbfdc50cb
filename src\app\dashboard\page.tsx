'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useAuth } from '@/components/providers/AuthProvider'
import { getBlogPosts, getProjects, getUploadedFiles } from '@/lib/firebase-operations'
import {
  DocumentTextIcon,
  RocketLaunchIcon,
  PhotoIcon,
  PlusIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline'

export default function DashboardPage() {
  const { user } = useAuth()
  const [stats, setStats] = useState({
    totalPosts: 0,
    publishedPosts: 0,
    totalProjects: 0,
    publishedProjects: 0,
    totalFiles: 0,
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user) {
      loadStats()
    }
  }, [user])

  const loadStats = async () => {
    if (!user) return

    try {
      const [posts, projects, files] = await Promise.all([
        getBlogPosts(user.uid),
        getProjects(user.uid),
        getUploadedFiles(user.uid)
      ])

      setStats({
        totalPosts: posts.length,
        publishedPosts: posts.filter(post => post.published).length,
        totalProjects: projects.length,
        publishedProjects: projects.filter(project => project.published).length,
        totalFiles: files.length,
      })
    } catch (error) {
      console.error('Error loading stats:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div>
        <h1 className="text-2xl font-bold text-slate-800 dark:text-slate-100">
          Welcome to Your Dashboard
        </h1>
        <p className="text-slate-600 dark:text-slate-400 mt-1">
          Manage your blog posts, projects, and media files.
        </p>
      </div>

      {/* Stats Overview */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <div key={i} className="bg-white dark:bg-slate-800 rounded-xl shadow-sm border border-slate-200 dark:border-slate-700 p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-1/2 mb-2"></div>
                <div className="h-8 bg-slate-200 dark:bg-slate-700 rounded w-1/3 mb-2"></div>
                <div className="h-3 bg-slate-200 dark:bg-slate-700 rounded w-2/3"></div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Blog Posts */}
          <div className="bg-white dark:bg-slate-800 rounded-xl shadow-sm border border-slate-200 dark:border-slate-700 p-6 hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-blue-50 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                  <DocumentTextIcon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-slate-600 dark:text-slate-400">Blog Posts</p>
                <p className="text-2xl font-semibold text-slate-900 dark:text-slate-100">{stats.totalPosts}</p>
                <p className="text-xs text-slate-500 dark:text-slate-500">
                  {stats.publishedPosts} published
                </p>
              </div>
            </div>
            <div className="mt-4">
              <Link
                href="/dashboard/posts"
                className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 flex items-center font-medium transition-colors"
              >
                Manage posts
                <ArrowRightIcon className="w-4 h-4 ml-1" />
              </Link>
            </div>
          </div>

          {/* Projects */}
          <div className="bg-white dark:bg-slate-800 rounded-xl shadow-sm border border-slate-200 dark:border-slate-700 p-6 hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-emerald-50 dark:bg-emerald-900/20 rounded-lg flex items-center justify-center">
                  <RocketLaunchIcon className="w-6 h-6 text-emerald-600 dark:text-emerald-400" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-slate-600 dark:text-slate-400">Projects</p>
                <p className="text-2xl font-semibold text-slate-900 dark:text-slate-100">{stats.totalProjects}</p>
                <p className="text-xs text-slate-500 dark:text-slate-500">
                  {stats.publishedProjects} published
                </p>
              </div>
            </div>
            <div className="mt-4">
              <Link
                href="/dashboard/projects"
                className="text-sm text-emerald-600 dark:text-emerald-400 hover:text-emerald-700 dark:hover:text-emerald-300 flex items-center font-medium transition-colors"
              >
                Manage projects
                <ArrowRightIcon className="w-4 h-4 ml-1" />
              </Link>
            </div>
          </div>

          {/* Media Files */}
          <div className="bg-white dark:bg-slate-800 rounded-xl shadow-sm border border-slate-200 dark:border-slate-700 p-6 hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-violet-50 dark:bg-violet-900/20 rounded-lg flex items-center justify-center">
                  <PhotoIcon className="w-6 h-6 text-violet-600 dark:text-violet-400" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-slate-600 dark:text-slate-400">Media Files</p>
                <p className="text-2xl font-semibold text-slate-900 dark:text-slate-100">{stats.totalFiles}</p>
                <p className="text-xs text-slate-500 dark:text-slate-500">
                  Images & documents
                </p>
              </div>
            </div>
            <div className="mt-4">
              <Link
                href="/dashboard/media"
                className="text-sm text-violet-600 dark:text-violet-400 hover:text-violet-700 dark:hover:text-violet-300 flex items-center font-medium transition-colors"
              >
                Manage media
                <ArrowRightIcon className="w-4 h-4 ml-1" />
              </Link>
            </div>
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="bg-white dark:bg-slate-800 rounded-xl shadow-sm border border-slate-200 dark:border-slate-700 p-6">
        <h2 className="text-lg font-semibold text-slate-800 dark:text-slate-100 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <Link
            href="/dashboard/posts/new"
            className="flex items-center justify-center px-4 py-6 border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-xl hover:border-blue-400 dark:hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 group"
          >
            <div className="text-center">
              <PlusIcon className="w-8 h-8 text-slate-400 group-hover:text-blue-500 dark:group-hover:text-blue-400 mx-auto mb-2 transition-colors" />
              <span className="text-sm font-medium text-slate-700 dark:text-slate-300 group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-colors">
                New Blog Post
              </span>
            </div>
          </Link>

          <Link
            href="/dashboard/projects/new"
            className="flex items-center justify-center px-4 py-6 border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-xl hover:border-emerald-400 dark:hover:border-emerald-500 hover:bg-emerald-50 dark:hover:bg-emerald-900/20 transition-all duration-200 group"
          >
            <div className="text-center">
              <PlusIcon className="w-8 h-8 text-slate-400 group-hover:text-emerald-500 dark:group-hover:text-emerald-400 mx-auto mb-2 transition-colors" />
              <span className="text-sm font-medium text-slate-700 dark:text-slate-300 group-hover:text-emerald-700 dark:group-hover:text-emerald-300 transition-colors">
                New Project
              </span>
            </div>
          </Link>

          <Link
            href="/dashboard/media"
            className="flex items-center justify-center px-4 py-6 border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-xl hover:border-violet-400 dark:hover:border-violet-500 hover:bg-violet-50 dark:hover:bg-violet-900/20 transition-all duration-200 group"
          >
            <div className="text-center">
              <PhotoIcon className="w-8 h-8 text-slate-400 group-hover:text-violet-500 dark:group-hover:text-violet-400 mx-auto mb-2 transition-colors" />
              <span className="text-sm font-medium text-slate-700 dark:text-slate-300 group-hover:text-violet-700 dark:group-hover:text-violet-300 transition-colors">
                Upload Media
              </span>
            </div>
          </Link>

          <Link
            href="/dashboard/analytics"
            className="flex items-center justify-center px-4 py-6 border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-xl hover:border-amber-400 dark:hover:border-amber-500 hover:bg-amber-50 dark:hover:bg-amber-900/20 transition-all duration-200 group"
          >
            <div className="text-center">
              <DocumentTextIcon className="w-8 h-8 text-slate-400 group-hover:text-amber-500 dark:group-hover:text-amber-400 mx-auto mb-2 transition-colors" />
              <span className="text-sm font-medium text-slate-700 dark:text-slate-300 group-hover:text-amber-700 dark:group-hover:text-amber-300 transition-colors">
                View Analytics
              </span>
            </div>
          </Link>
        </div>
      </div>

      {/* Status */}
      <div className="bg-emerald-50 dark:bg-emerald-900/20 border border-emerald-200 dark:border-emerald-800 rounded-xl p-6">
        <h2 className="text-lg font-semibold text-emerald-900 dark:text-emerald-100 mb-2">
          🎉 Dashboard Ready!
        </h2>
        <p className="text-emerald-800 dark:text-emerald-200 mb-4">
          Your content management system is fully operational and ready to use.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-emerald-700 dark:text-emerald-300">
          <div>
            <p>✅ Firebase Authentication</p>
            <p>✅ Blog Post Management</p>
            <p>✅ Project Showcase</p>
          </div>
          <div>
            <p>✅ Media Library</p>
            <p>✅ Preview Functionality</p>
            <p>✅ Publishing Controls</p>
          </div>
        </div>
      </div>
    </div>
  )
}
