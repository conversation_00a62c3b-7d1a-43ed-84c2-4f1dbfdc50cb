'use client'

import { useState } from 'react'
import BlogGrid from './BlogGrid'
import BlogFilter from './BlogFilter'
import { BlogPost } from '@/types'

interface BlogGridWithFilterProps {
  posts: BlogPost[]
}

export default function BlogGridWithFilter({ posts }: BlogGridWithFilterProps) {
  const [filteredPosts, setFilteredPosts] = useState<BlogPost[]>(posts)

  const handleFilterChange = (filtered: BlogPost[]) => {
    setFilteredPosts(filtered)
  }

  return (
    <>
      {/* Filter */}
      <BlogFilter posts={posts} onFilterChange={handleFilterChange} />

      {/* Blog Posts Grid */}
      <BlogGrid posts={filteredPosts} />
    </>
  )
}
