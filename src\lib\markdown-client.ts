'use client'

import { remark } from 'remark'
import html from 'remark-html'
import remarkGfm from 'remark-gfm'

// Client-side markdown processor (no Node.js dependencies)
export async function processMarkdownContent(content: string): Promise<string> {
  try {
    const processedContent = await remark()
      .use(remarkGfm)
      .use(html)
      .process(content)
    return processedContent.toString()
  } catch (error) {
    console.error('Error processing markdown:', error)
    // Return content with basic line breaks if markdown processing fails
    return content.replace(/\n/g, '<br>')
  }
}
