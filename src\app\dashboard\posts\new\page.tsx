'use client'

import Link from 'next/link'
import BlogPostForm from '@/components/dashboard/BlogPostForm'
import { ArrowLeftIcon } from '@heroicons/react/24/outline'

export default function NewBlogPostPage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link
            href="/dashboard/posts"
            className="inline-flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
          >
            <ArrowLeftIcon className="w-5 h-5 mr-2" />
            Back to Posts
          </Link>
        </div>
      </div>

      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Create New Blog Post
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Write and publish a new blog post.
        </p>
      </div>

      {/* Form */}
      <BlogPostForm mode="create" />
    </div>
  )
}
