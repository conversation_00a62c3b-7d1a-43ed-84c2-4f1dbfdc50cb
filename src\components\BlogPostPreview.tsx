'use client'

import { useState } from 'react'
import Image from 'next/image'
import { processMarkdownContent } from '@/lib/markdown-client'

interface BlogPostPreviewProps {
  title: string
  content: string
  featuredImage?: string
  excerpt?: string
  tags?: string[]
  author?: string
  publishedAt?: string
}

export default function BlogPostPreview({
  title,
  content,
  featuredImage,
  excerpt,
  tags,
  author,
  publishedAt
}: BlogPostPreviewProps) {
  const [processedContent, setProcessedContent] = useState('')
  const [loading, setLoading] = useState(false)

  const handlePreview = async () => {
    setLoading(true)
    try {
      const processed = await processMarkdownContent(content)
      setProcessedContent(processed)
    } catch (error) {
      console.error('Error processing content:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
    
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-900 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Blog Post Preview</h2>
          <div className="flex space-x-3">
            <button
              onClick={handlePreview}
              disabled={loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              {loading ? 'Processing...' : 'Process Content'}
            </button>
            <button
              onClick={() => window.dispatchEvent(new CustomEvent('close-preview'))}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              Close
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="overflow-y-auto max-h-[calc(90vh-120px)]">
          <div className="bg-white dark:bg-gray-900">
            <div className="max-w-4xl mx-auto px-6 py-8">
              {/* Featured Image */}
              {featuredImage && featuredImage.startsWith('http') && (
                <div className="mb-8">
                  <Image
                    src={featuredImage}
                    alt={title}
                    width={800}
                    height={400}
                    className="w-full h-64 object-cover rounded-lg"
                  />
                </div>
              )}

              {/* Title */}
              <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                {title || 'Untitled Post'}
              </h1>

              {/* Meta */}
              <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 mb-6">
                <span>By {author || 'Author'}</span>
                <span className="mx-2">•</span>
                <span>{formatDate(publishedAt)}</span>
              </div>

              {/* Excerpt */}
              {excerpt && (
                <div className="text-lg text-gray-600 dark:text-gray-300 mb-8 italic">
                  {excerpt}
                </div>
              )}

              {/* Tags */}
              {tags && tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-8">
                  {tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-sm rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              )}

              {/* Content */}
              <div className="prose prose-lg max-w-none dark:prose-invert">
                {processedContent ? (
                  <div dangerouslySetInnerHTML={{ __html: processedContent }} />
                ) : (
                  <div className="whitespace-pre-wrap text-gray-700 dark:text-gray-300">
                    {content || 'No content available'}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
