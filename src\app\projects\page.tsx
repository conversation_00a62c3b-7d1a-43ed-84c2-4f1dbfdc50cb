'use client'

import { useEffect, useState } from 'react'
import { getProjects } from '@/lib/firebase-operations'
import { processMarkdownContent } from '@/lib/markdown-client'
import ProjectGrid from '@/components/ProjectGrid'
import SubtleGradientBackground from '@/components/SubtleGradientBackground'
import { Project, DatabaseProject } from '@/types'

// Convert Firebase projects to Project format
async function convertFirebaseProjectToProject(firebaseProject: DatabaseProject): Promise<Project> {
  // Process markdown content
  const processedContent = await processMarkdownContent(firebaseProject.content || '')

  // Safely handle technologies field
  let technologies: string[] = []
  if (firebaseProject.technology_used) {
    if (typeof firebaseProject.technology_used === 'string') {
      technologies = firebaseProject.technology_used.split(',').map(t => t.trim())
    } else if (Array.isArray(firebaseProject.technology_used)) {
      technologies = firebaseProject.technology_used
    }
  } else if (firebaseProject.tech_stack && Array.isArray(firebaseProject.tech_stack)) {
    technologies = firebaseProject.tech_stack
  }

  return {
    slug: firebaseProject.slug,
    title: firebaseProject.title,
    description: firebaseProject.description || firebaseProject.excerpt || '',
    featuredImage: firebaseProject.featured_image || '/images/projects/default.jpg',
    images: [],
    technologies: technologies,
    liveUrl: firebaseProject.live_site_url || firebaseProject.project_url,
    githubUrl: firebaseProject.source_code_url || firebaseProject.github_url,
    content: processedContent,
    date: firebaseProject.created_at,
    category: 'Web Development',
    client: firebaseProject.client || '',
    industry: firebaseProject.industry || '',
    challenge: firebaseProject.challenge || '',
    strategy: firebaseProject.solution || '',
  }
}

export default function ProjectsPage() {
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function loadProjects() {
      try {
        // Get projects from Firebase only
        const firebaseProjects = await getProjects()

        // Convert Firebase projects to Project format and filter published projects
        const publishedProjects = firebaseProjects.filter(project => project.published)
        const convertedProjects = await Promise.all(
          publishedProjects.map(project => convertFirebaseProjectToProject(project))
        )
        const sortedProjects = convertedProjects.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())

        setProjects(sortedProjects)
      } catch (error) {
        console.error('Error loading projects:', error)
        setProjects([])
      } finally {
        setLoading(false)
      }
    }

    loadProjects()
  }, [])

  // Structured data for SEO
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": "Ernst Romelo Projects",
    "description": "Portfolio of web development and AI automation projects",
    "url": "https://ernestomelo.com/projects",
    "author": {
      "@type": "Person",
      "name": "Ernst Romelo",
      "url": "https://ernestomelo.com"
    },
    "mainEntity": {
      "@type": "ItemList",
      "itemListElement": projects.map((project, index) => ({
        "@type": "CreativeWork",
        "position": index + 1,
        "name": project.title,
        "description": project.description,
        "url": `https://ernestomelo.com/projects/${project.slug}`,
        "image": project.featuredImage,
        "author": {
          "@type": "Person",
          "name": "Ernst Romelo"
        },
        "dateCreated": project.date
      }))
    }
  }

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <SubtleGradientBackground />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-gray-100 mb-6 animate-fade-in">
            <span className="bg-gradient-to-r from-blue-500 via-purple-500 to-orange-400 dark:from-blue-400 dark:via-purple-400 dark:to-orange-300 bg-clip-text text-transparent">
              My Projects
            </span>
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto animate-slide-up">
            Explore my portfolio of web development, AI automation, and technology solutions.
            Each project showcases real-world problem-solving with modern technologies.
          </p>
        </div>

        {/* Projects Grid */}
        <div className="mb-20">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <ProjectGrid projects={projects} />
          )}
        </div>
      </div>
    </>
  )
}
