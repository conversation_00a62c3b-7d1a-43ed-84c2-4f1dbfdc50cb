# Email Setup Guide

## 🚨 IMPORTANT: Your forms are currently NOT sending real emails!

To receive form submissions in your inbox, you need to set up EmailJS. Follow these steps:

## Step 1: Create EmailJS Account

1. Go to [EmailJS.com](https://www.emailjs.com/)
2. Sign up for a free account
3. Verify your email address

## Step 2: Set Up Email Service

1. In EmailJS dashboard, go to **Email Services**
2. Click **Add New Service**
3. Choose your email provider (Gmail, Outlook, etc.) 
4. Follow the setup instructions
5. **Copy your Service ID** (you'll need this)

## Step 3: Create Email Templates

### Contact Form Template:
1. Go to **Email Templates** → **Create New Template**
2. Template Name: `Contact Form Submission`
3. Template content:
```
Subject: New Contact from {{from_name}}

From: {{from_name}} ({{from_email}})
Subject: {{subject}}

Message:
{{message}}

---
Reply to: {{reply_to}}
```

4. **Copy the Template ID** 

### Project Inquiry Template:
1. Create another template: `Project Inquiry Submission`
2. Template content:
```
Subject: New Project Inquiry from {{from_name}}

Contact Information:
- Name: {{from_name}}
- Email: {{from_email}}
- Phone: {{phone}}
- Company: {{company}}
- Website: {{website}}

Project Details:
- Service: {{service}}
- Budget: {{budget}}
- Deadline: {{deadline}}
- How they heard about us: {{hear_about}}

Project Description:
{{project_description}}

---
Reply to: {{reply_to}}
```

3. **Copy this Template ID too** 

## Step 4: Get Your Public Key

1. Go to **Account** → **General**
2. Find your **Public Key**
3. **Copy it** 

## Step 5: Configure Environment Variables

1. In your project folder, create a file called `.env.local`
2. Copy the content from `.env.local.example`
3. Replace the placeholder values with your actual EmailJS credentials:

```env
NEXT_PUBLIC_EMAILJS_SERVICE_ID=
NEXT_PUBLIC_EMAILJS_TEMPLATE_ID_CONTACT=
NEXT_PUBLIC_EMAILJS_TEMPLATE_ID_PROJECT=
NEXT_PUBLIC_EMAILJS_PUBLIC_KEY=
```

## Step 6: Test Your Setup

1. Restart your development server: `npm run dev`
2. Go to your website
3. Fill out a contact form or project inquiry
4. Check your email inbox!

## 🎉 That's it!

Once configured, all form submissions will be sent directly to **<EMAIL>**

## Alternative Options

If you prefer other email services:

### Option 2: Resend
- More developer-friendly
- Requires backend API route
- Better for high-volume emails

### Option 3: Netlify Forms
- If you deploy on Netlify
- Built-in form handling
- No configuration needed

### Option 4: Formspree
- Simple form backend
- Just change form action
- Free tier available

## Need Help?

If you run into issues, the most common problems are:
1. Wrong template variable names
2. Incorrect service/template IDs
3. Email service not properly connected
4. Environment variables not loaded (restart server)

EmailJS free tier includes 200 emails/month, which should be plenty for a personal blog.
