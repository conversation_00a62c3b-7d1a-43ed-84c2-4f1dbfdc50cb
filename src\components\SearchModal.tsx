'use client'

import { useState, useEffect, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Search, X, FileText, Folder } from 'lucide-react'
import Link from 'next/link'

interface SearchModalProps {
  isOpen: boolean
  onClose: () => void
}

type SearchResult = {
  type: 'blog' | 'project'
  title: string
  excerpt: string
  slug: string
  date: string
  categories?: string[]
  technologies?: string[]
}

export default function SearchModal({ isOpen, onClose }: SearchModalProps) {
  const [query, setQuery] = useState('')
  const [searchResults, setSearchResults] = useState<SearchResult[]>([])
  const [isLoading, setIsLoading] = useState(false)

  // Debounced search function
  useEffect(() => {
    if (!query.trim()) {
      setSearchResults([])
      return
    }

    const timeoutId = setTimeout(async () => {
      setIsLoading(true)
      try {
        const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`)
        const data = await response.json()
        setSearchResults(data.results || [])
      } catch (error) {
        console.error('Search error:', error)
        setSearchResults([])
      } finally {
        setIsLoading(false)
      }
    }, 300) // 300ms debounce

    return () => clearTimeout(timeoutId)
  }, [query])

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, onClose])

  // Reset query when modal closes
  useEffect(() => {
    if (!isOpen) {
      setQuery('')
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-gray-900/90 backdrop-blur-md z-50 flex items-start justify-center pt-20"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0, y: -30 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.9, opacity: 0, y: -30 }}
            className="bg-gray-900/95 dark:bg-gray-800/95 backdrop-blur-md rounded-2xl shadow-2xl w-full max-w-2xl mx-4 max-h-[80vh] overflow-hidden border border-gray-700/50 dark:border-gray-600/50"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center p-6 border-b border-gray-700/50 dark:border-gray-600/50">
              <Search className="w-6 h-6 text-gray-400 mr-4" />
              <input
                type="text"
                placeholder="Search blog posts and projects..."
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                className="flex-1 bg-transparent text-white placeholder-gray-400 outline-none text-lg font-medium"
                autoFocus
              />
              <motion.button
                onClick={onClose}
                className="ml-4 p-2 text-gray-400 hover:text-white hover:bg-gray-700/50 rounded-lg transition-all duration-200"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <X className="w-5 h-5" />
              </motion.button>
            </div>

            {/* Results */}
            <div className="overflow-y-auto max-h-96">
              {isLoading ? (
                <div className="p-8 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                  <p className="text-gray-400 mt-4">Loading...</p>
                </div>
              ) : !query.trim() ? (
                <div className="p-8 text-center">
                  <Search className="w-12 h-12 text-gray-500 mx-auto mb-4" />
                  <p className="text-gray-400">Start typing to search blog posts and projects</p>
                </div>
              ) : searchResults.length === 0 ? (
                <div className="p-8 text-center">
                  <p className="text-gray-400">No results found for "{query}"</p>
                </div>
              ) : (
                <div className="p-3">
                  {searchResults.map((result, index) => (
                    <motion.div
                      key={`${result.type}-${result.slug}`}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.05 }}
                    >
                      <Link
                        href={result.type === 'blog' ? `/blog/${result.slug}` : `/projects/${result.slug}`}
                        onClick={onClose}
                        className="no-link-style block p-4 hover:bg-gray-700/50 rounded-xl transition-all duration-200 group"
                      >
                        <div className="flex items-start space-x-4">
                          <div className="flex-shrink-0 mt-1">
                            {result.type === 'blog' ? (
                              <FileText className="w-5 h-5 text-blue-400 group-hover:text-blue-300 transition-colors" />
                            ) : (
                              <Folder className="w-5 h-5 text-orange-400 group-hover:text-orange-300 transition-colors" />
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <h3 className="text-white font-medium truncate group-hover:text-blue-300 transition-colors">
                              {result.title}
                            </h3>
                            <p className="text-gray-400 text-sm mt-1 line-clamp-2">
                              {result.excerpt}
                            </p>
                            <div className="flex items-center mt-2 text-xs text-gray-500">
                              <span className="capitalize px-2 py-1 bg-gray-700/50 rounded-md text-gray-300">
                                {result.type}
                              </span>
                              <span className="mx-2">•</span>
                              <span>{new Date(result.date).toLocaleDateString()}</span>
                              {(result.categories || result.technologies) && (
                                <>
                                  <span className="mx-2">•</span>
                                  <span className="truncate">
                                    {(result.categories || result.technologies)?.slice(0, 2).join(', ')}
                                  </span>
                                </>
                              )}
                            </div>
                          </div>
                        </div>
                      </Link>
                    </motion.div>
                  ))}
                </div>
              )}
            </div>

            {/* Footer */}
            {searchResults.length > 0 && (
              <div className="p-4 border-t border-gray-700/50 text-center">
                <p className="text-xs text-gray-400">
                  {searchResults.length} result{searchResults.length !== 1 ? 's' : ''} found
                </p>
              </div>
            )}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
