# Manual Start Guide

Since we're having issues with the automated terminal execution, here's how to manually start your blog website:

## Method 1: Using Command Prompt

1. **Open Command Prompt as Administrator**
   - Press `Windows + R`
   - Type `cmd` and press `Ctrl + Shift + Enter`

2. **Navigate to your project directory**
   ```cmd
   cd "C:\Users\<USER>\Documents\MyPrograms\AugmentBlog"
   ```

3. **Install dependencies (if not already done)**
   ```cmd
   "C:\Program Files\nodejs\npm.cmd" install
   ```

4. **Start the development server**
   ```cmd
   "C:\Program Files\nodejs\npm.cmd" run dev
   ```

5. **Open your browser**
   - Go to: http://localhost:3000

## Method 2: Using the Batch File

1. **Navigate to your project folder**
   - Open File Explorer
   - Go to: `C:\Users\<USER>\Documents\MyPrograms\AugmentBlog`

2. **Double-click on `start-dev.bat`**
   - This should open a command prompt and start the server

3. **Open your browser**
   - Go to: http://localhost:3000

## Method 3: Using PowerShell

1. **Open PowerShell as Administrator**
   - Right-click Start button
   - Select "Windows PowerShell (Admin)"

2. **Navigate to project directory**
   ```powershell
   cd "C:\Users\<USER>\Documents\MyPrograms\AugmentBlog"
   ```

3. **Start the server**
   ```powershell
   & "C:\Program Files\nodejs\npm.cmd" run dev
   ```

4. **Open your browser**
   - Go to: http://localhost:3000

## What You Should See

When the server starts successfully, you should see output like:
```
> ernest-romelo-blog@0.1.0 dev
> next dev

   ▲ Next.js 14.0.4
   - Local:        http://localhost:3000
   - Environments: .env.local

 ✓ Ready in 2.3s
```

## Troubleshooting

### If you get "npm is not recognized":
- Node.js is not in your PATH
- Use the full path: `"C:\Program Files\nodejs\npm.cmd"`

### If you get port 3000 already in use:
- Another application is using port 3000
- Try: `npm run dev -- --port 3001`
- Then go to: http://localhost:3001

### If you get TypeScript errors:
- The project should still run in development mode
- Check the browser console for any runtime errors

### If the page doesn't load:
- Make sure the server is running (check the terminal)
- Try refreshing the browser
- Check if Windows Firewall is blocking the connection

## Expected Website Features

Once running, you should see:
- ✅ Clean, modern homepage with blog grid
- ✅ Sample "Welcome to My Blog" post
- ✅ Projects section with sample project
- ✅ Contact modal (click "Contact Me" button)
- ✅ Responsive design
- ✅ Smooth animations

## Next Steps After It's Running

1. **Replace placeholder images**
   - Add your avatar to `public/images/avatar.jpg`
   - Add blog images to `public/images/blog/`
   - Add project images to `public/images/projects/`

2. **Create your content**
   - Use templates in `content/blog/template.md`
   - Use templates in `content/projects/template.md`

3. **Customize the design**
   - Edit colors in `tailwind.config.ts`
   - Update personal info in components

4. **Deploy when ready**
   - Push to GitHub
   - Deploy to Vercel or Netlify
   - Connect your domain

## Need Help?

If you're still having issues:
1. Check that Node.js is properly installed
2. Make sure you're in the correct directory
3. Try restarting your computer
4. Check Windows Defender/Antivirus settings

The website is fully functional and ready to run - we just need to get the development server started!
