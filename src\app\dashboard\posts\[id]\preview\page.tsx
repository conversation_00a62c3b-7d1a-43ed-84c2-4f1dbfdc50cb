'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { getBlogPost } from '@/lib/firebase-operations'
import { DatabaseBlogPost } from '@/types'
import { processMarkdownContent } from '@/lib/markdown-client'
import { ArrowLeftIcon, PencilIcon, EyeIcon } from '@heroicons/react/24/outline'

interface PreviewBlogPostPageProps {
  params: {
    id: string
  }
}

export default function PreviewBlogPostPage({ params }: PreviewBlogPostPageProps) {
  const [post, setPost] = useState<DatabaseBlogPost | null>(null)
  const [processedContent, setProcessedContent] = useState('')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadPost()
  }, [params.id])

  const loadPost = async () => {
    try {
      const postData = await getBlogPost(params.id)
      if (postData) {
        setPost(postData)
        const processed = await processMarkdownContent(postData.content)
        setProcessedContent(processed)
      }
    } catch (error) {
      console.error('Error loading post:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!post) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Post not found
        </h2>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          The blog post you're looking for doesn't exist.
        </p>
        <Link
          href="/dashboard/posts"
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <ArrowLeftIcon className="w-5 h-5 mr-2" />
          Back to Posts
        </Link>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link
            href="/dashboard/posts"
            className="inline-flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
          >
            <ArrowLeftIcon className="w-5 h-5 mr-2" />
            Back to Posts
          </Link>
        </div>
        
        <div className="flex items-center space-x-3">
          <Link
            href={`/dashboard/posts/${params.id}/edit`}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <PencilIcon className="w-4 h-4 mr-2" />
            Edit Post
          </Link>
        </div>
      </div>

      {/* Preview Notice */}
      <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
        <div className="flex items-center">
          <EyeIcon className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2" />
          <div>
            <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
              Preview Mode
            </h3>
            <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
              This is how your blog post will appear to readers. 
              {!post.published && ' This post is currently a draft and not visible to the public.'}
            </p>
          </div>
        </div>
      </div>

      {/* Post Preview */}
      <article className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
        {/* Featured Image */}
        {post.featured_image && (
          <div className="aspect-video w-full bg-gray-200 dark:bg-gray-700">
            <img
              src={post.featured_image}
              alt={post.title}
              className="w-full h-full object-cover"
            />
          </div>
        )}

        <div className="p-8">
          {/* Post Meta */}
          <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 mb-4">
            <span>Published on {formatDate(post.created_at)}</span>
            {post.reading_time && (
              <>
                <span className="mx-2">•</span>
                <span>{post.reading_time} min read</span>
              </>
            )}
            {!post.published && (
              <>
                <span className="mx-2">•</span>
                <span className="text-yellow-600 dark:text-yellow-400 font-medium">Draft</span>
              </>
            )}
          </div>

          {/* Title */}
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            {post.title}
          </h1>

          {/* Excerpt */}
          {post.excerpt && (
            <p className="text-lg text-gray-600 dark:text-gray-400 mb-8 leading-relaxed">
              {post.excerpt}
            </p>
          )}

          {/* Tags */}
          {post.tags && post.tags.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-8">
              {post.tags.map((tag) => (
                <span
                  key={tag}
                  className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200"
                >
                  {tag}
                </span>
              ))}
            </div>
          )}

          {/* Content */}
          <div 
            className="prose dark:prose-invert max-w-none prose-lg"
            dangerouslySetInnerHTML={{ __html: processedContent }}
          />
        </div>
      </article>
    </div>
  )
}
