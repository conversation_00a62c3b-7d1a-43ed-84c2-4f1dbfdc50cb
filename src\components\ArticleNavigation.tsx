import Link from 'next/link'

interface Article {
  slug: string
  title: string
}

interface ArticleNavigationProps {
  previousArticle?: Article
  nextArticle?: Article
}

export default function ArticleNavigation({ previousArticle, nextArticle }: ArticleNavigationProps) {
  return (
    <div className="border-t border-gray-200 pt-8 mt-12">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Previous Article */}
        <div className="flex">
          {previousArticle ? (
            <Link
              href={`/blog/${previousArticle.slug}`}
              className="no-link-style group flex items-start space-x-4 p-6 bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-xl transition-colors duration-200 w-full"
            >
              <div className="flex-shrink-0 mt-1">
                <svg className="w-5 h-5 text-gray-400 dark:text-gray-500 group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2">
                  Previous Article
                </p>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-200 line-clamp-2">
                  {previousArticle.title}
                </h3>
              </div>
            </Link>
          ) : (
            <div className="w-full"></div>
          )}
        </div>

        {/* Next Article */}
        <div className="flex">
          {nextArticle ? (
            <Link
              href={`/blog/${nextArticle.slug}`}
              className="no-link-style group flex items-start space-x-4 p-6 bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-xl transition-colors duration-200 w-full text-right"
            >
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2">
                  Next Article
                </p>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-200 line-clamp-2">
                  {nextArticle.title}
                </h3>
              </div>
              <div className="flex-shrink-0 mt-1">
                <svg className="w-5 h-5 text-gray-400 dark:text-gray-500 group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </Link>
          ) : (
            <div className="w-full"></div>
          )}
        </div>
      </div>
    </div>
  )
}
