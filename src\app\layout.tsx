import type { Metadata } from 'next'
import { <PERSON><PERSON>_<PERSON> } from 'next/font/google'
import './globals.css'
import Header from '@/components/Header'
import Footer from '@/components/Footer'
import ContactModal from '@/components/ContactModal'
import LeftSidebar from '@/components/LeftSidebar'
import { ThemeProvider } from '@/components/providers/ThemeProvider'
import { AuthProvider } from '@/components/providers/AuthProvider'

import { Analytics } from '@vercel/analytics/next'
import { SpeedInsights } from '@vercel/speed-insights/next'

const dmSans = DM_Sans({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
  display: 'swap',
})

export const metadata: Metadata = {
  metadataBase: new URL('http://localhost:3000'),
  title: '<PERSON> - Personal Blog & Portfolio',
  description: 'Personal blog and portfolio of <PERSON> - Web Developer & AI Automation Specialist',
  keywords: '<PERSON>, web development, AI automation, blog, portfolio',
  authors: [{ name: '<PERSON>' }],
  icons: {
    icon: '/favicon.webp',
    shortcut: '/favicon.webp',
    apple: '/favicon.webp',
  },
  openGraph: {
    title: 'Ernst Romelo - Personal Blog & Portfolio',
    description: 'Personal blog and portfolio of Ernst Romelo - Web Developer & AI Automation Specialist',
    url: 'https://ernestromelo.com',
    siteName: 'Ernst Romelo',
    type: 'website',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </head>
      <body className={dmSans.className}>
        <AuthProvider>
          <ThemeProvider
            defaultTheme="dark"
            storageKey="blog-theme"
          >
          <div className="min-h-screen relative flex flex-col bg-gradient-to-br from-blue-50/80 via-white to-orange-50/60 dark:from-gray-900/80 dark:via-gray-800 dark:to-gray-900/60">
            {/* Subtle Background Pattern */}
            <div className="fixed inset-0 bg-grid-pattern-light dark:bg-grid-pattern-dark opacity-30 pointer-events-none z-0"></div>
            <div className="fixed inset-0 bg-gradient-to-r from-transparent via-blue-50/20 dark:via-gray-800/20 to-transparent pointer-events-none z-0"></div>

            {/* Left Sidebar */}
            <LeftSidebar />

            <Header />
            <main className="pt-20 relative z-10 flex-grow">
              {children}
            </main>
            <Footer />
            <ContactModal />
          </div>
        </ThemeProvider>
        </AuthProvider>
        {/* Analytics - wrapped in try/catch to prevent errors */}
        {process.env.NODE_ENV === 'production' && (
          <>
            <Analytics />
            <SpeedInsights />
          </>
        )}
      </body>
    </html>
  )
}
