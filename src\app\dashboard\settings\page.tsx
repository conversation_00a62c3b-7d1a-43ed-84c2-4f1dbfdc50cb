'use client'

import { useState } from 'react'
import { useAuth } from '@/components/providers/AuthProvider'
import { updateProfile } from 'firebase/auth'
import { 
  UserIcon, 
  EnvelopeIcon, 
  KeyIcon,
  CheckIcon
} from '@heroicons/react/24/outline'

export default function SettingsPage() {
  const { user, resetPassword } = useAuth()
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [formData, setFormData] = useState({
    displayName: user?.displayName || '',
    email: user?.email || '',
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    setLoading(true)
    setMessage('')

    try {
      await updateProfile(user, {
        displayName: formData.displayName
      })
      setMessage('Profile updated successfully!')
    } catch (error) {
      console.error('Error updating profile:', error)
      setMessage('Failed to update profile')
    } finally {
      setLoading(false)
    }
  }

  const handlePasswordReset = async () => {
    if (!user?.email) return

    setLoading(true)
    setMessage('')

    try {
      await resetPassword(user.email)
      setMessage('Password reset email sent! Check your inbox.')
    } catch (error) {
      console.error('Error sending password reset:', error)
      setMessage('Failed to send password reset email')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Settings
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Manage your account settings and preferences.
        </p>
      </div>

      {/* Message */}
      {message && (
        <div className={`p-4 rounded-lg ${
          message.includes('successfully') || message.includes('sent')
            ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-800 dark:text-green-200'
            : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200'
        }`}>
          <div className="flex items-center">
            <CheckIcon className="w-5 h-5 mr-2" />
            {message}
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Profile Settings */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center mb-6">
            <UserIcon className="w-6 h-6 text-gray-600 dark:text-gray-400 mr-3" />
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Profile Information
            </h2>
          </div>

          <form onSubmit={handleUpdateProfile} className="space-y-4">
            <div>
              <label htmlFor="displayName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Display Name
              </label>
              <input
                type="text"
                id="displayName"
                name="displayName"
                value={formData.displayName}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                placeholder="Your display name"
              />
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Email Address
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                disabled
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Email cannot be changed from this interface
              </p>
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Updating...' : 'Update Profile'}
            </button>
          </form>
        </div>

        {/* Security Settings */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center mb-6">
            <KeyIcon className="w-6 h-6 text-gray-600 dark:text-gray-400 mr-3" />
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Security
            </h2>
          </div>

          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Password
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Reset your password by sending a reset link to your email address.
              </p>
              <button
                onClick={handlePasswordReset}
                disabled={loading}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Sending...' : 'Send Password Reset Email'}
              </button>
            </div>
          </div>
        </div>

        {/* Account Information */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center mb-6">
            <EnvelopeIcon className="w-6 h-6 text-gray-600 dark:text-gray-400 mr-3" />
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Account Information
            </h2>
          </div>

          <div className="space-y-4 text-sm">
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">User ID:</span>
              <p className="text-gray-600 dark:text-gray-400 font-mono text-xs break-all">
                {user?.uid}
              </p>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Email Verified:</span>
              <p className="text-gray-600 dark:text-gray-400">
                {user?.emailVerified ? (
                  <span className="text-green-600 dark:text-green-400">✓ Verified</span>
                ) : (
                  <span className="text-yellow-600 dark:text-yellow-400">⚠ Not verified</span>
                )}
              </p>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Account Created:</span>
              <p className="text-gray-600 dark:text-gray-400">
                {user?.metadata.creationTime ? 
                  new Date(user.metadata.creationTime).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  }) : 'Unknown'
                }
              </p>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Last Sign In:</span>
              <p className="text-gray-600 dark:text-gray-400">
                {user?.metadata.lastSignInTime ? 
                  new Date(user.metadata.lastSignInTime).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  }) : 'Unknown'
                }
              </p>
            </div>
          </div>
        </div>

        {/* Site Configuration */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
            Site Configuration
          </h2>
          
          <div className="space-y-4 text-sm">
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Authentication:</span>
              <p className="text-green-600 dark:text-green-400">✓ Firebase Auth Active</p>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Database:</span>
              <p className="text-green-600 dark:text-green-400">✓ Firestore Connected</p>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Storage:</span>
              <p className="text-green-600 dark:text-green-400">✓ Firebase Storage Active</p>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Dashboard:</span>
              <p className="text-green-600 dark:text-green-400">✓ Content Management Ready</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
