'use client'

import { useState } from 'react'
import Image from 'next/image'
import { processMarkdownContent } from '@/lib/markdown-client'

interface ProjectPreviewProps {
  title: string
  description: string
  content: string
  featuredImage?: string
  technologies?: string[]
  projectUrl?: string
  githubUrl?: string
  client?: string
  industry?: string
  challenge?: string
  solution?: string
  publishedAt?: string
}

export default function ProjectPreview({
  title,
  description,
  content,
  featuredImage,
  technologies,
  projectUrl,
  githubUrl,
  client,
  industry,
  challenge,
  solution,
  publishedAt
}: ProjectPreviewProps) {
  const [processedContent, setProcessedContent] = useState('')
  const [loading, setLoading] = useState(false)

  const handlePreview = async () => {
    setLoading(true)
    try {
      const processed = await processMarkdownContent(content)
      setProcessedContent(processed)
    } catch (error) {
      console.error('Error processing content:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long'
    })
    
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long'
    })
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-900 rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Project Preview</h2>
          <div className="flex space-x-3">
            <button
              onClick={handlePreview}
              disabled={loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              {loading ? 'Processing...' : 'Process Content'}
            </button>
            <button
              onClick={() => window.dispatchEvent(new CustomEvent('close-preview'))}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              Close
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="overflow-y-auto max-h-[calc(90vh-120px)]">
          <div className="bg-white dark:bg-gray-900">
            <div className="max-w-6xl mx-auto px-6 py-8">
              {/* Technologies */}
              {technologies && technologies.length > 0 && (
                <div className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  {technologies.map((tech, index) => (
                    <span key={tech}>
                      {tech}
                      {index < technologies.length - 1 && ' / '}
                    </span>
                  ))}
                </div>
              )}

              {/* Title */}
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-8">
                {title || 'Untitled Project'}
              </h1>

              {/* Featured Image */}
              {featuredImage && featuredImage.startsWith('http') && (
                <div className="mb-12">
                  <Image
                    src={featuredImage}
                    alt={title}
                    width={1200}
                    height={630}
                    className="w-full h-80 sm:h-96 md:h-auto object-cover rounded-lg"
                  />
                </div>
              )}

              {/* Content and Sidebar Layout */}
              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-8 lg:gap-12">
                {/* Main Content */}
                <div className="md:col-span-2 lg:col-span-3">
                  {/* Description */}
                  {description && (
                    <div className="text-lg text-gray-600 dark:text-gray-300 mb-8">
                      {description}
                    </div>
                  )}

                  {/* Content */}
                  <div className="prose prose-lg max-w-none dark:prose-invert">
                    {processedContent ? (
                      <div dangerouslySetInnerHTML={{ __html: processedContent }} />
                    ) : (
                      <div className="whitespace-pre-wrap text-gray-700 dark:text-gray-300">
                        {content || 'No content available'}
                      </div>
                    )}
                  </div>
                </div>

                {/* Sidebar - Metadata */}
                <div className="md:col-span-1 lg:col-span-1">
                  <div className="space-y-6">
                    {/* Project Date */}
                    <div>
                      <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-2">Project Date</h3>
                      <p className="text-gray-600 dark:text-gray-300 text-sm">
                        {formatDate(publishedAt)}
                      </p>
                    </div>

                    {/* Client */}
                    {client && (
                      <div>
                        <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-2">Client</h3>
                        <p className="text-gray-600 dark:text-gray-300 text-sm">{client}</p>
                      </div>
                    )}

                    {/* Industry */}
                    {industry && (
                      <div>
                        <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-2">Industry</h3>
                        <p className="text-gray-600 dark:text-gray-300 text-sm">{industry}</p>
                      </div>
                    )}

                    {/* Technology Used */}
                    {technologies && technologies.length > 0 && (
                      <div>
                        <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-2">Technology Used</h3>
                        <div className="space-y-1">
                          {technologies.map((tech) => (
                            <p key={tech} className="text-gray-600 dark:text-gray-300 text-sm">{tech}</p>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* The Challenge */}
                    {challenge && (
                      <div>
                        <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-2">The Challenge</h3>
                        <p className="text-gray-600 dark:text-gray-300 text-sm">
                          {challenge}
                        </p>
                      </div>
                    )}

                    {/* The Solution */}
                    {solution && (
                      <div>
                        <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-2">The Solution</h3>
                        <p className="text-gray-600 dark:text-gray-300 text-sm">
                          {solution}
                        </p>
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="space-y-3 pt-4">
                      {projectUrl && (
                        <div className="w-full px-4 py-3 bg-blue-600 text-white text-sm font-medium text-center rounded-md">
                          Visit Live Site ↗
                        </div>
                      )}
                      {githubUrl && (
                        <div className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 text-sm font-medium text-center rounded-md">
                          View Code
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
